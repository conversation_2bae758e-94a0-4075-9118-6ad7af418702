# KYC Onboarding API Integration Guide

## Overview

This guide explains how to use the KYC onboarding APIs for creating and managing client verification processes. The system supports a multi-step KYC flow where users can create multiple KYC applications for different clients.

## API Endpoints

### Base URL
```
http://**********:8001/v1/services
```

### Authentication
All API calls require authentication using Bearer tokens. Include the token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Onboarding Process Flow

### 1. Initial Onboarding User Creation

When a user starts the KYC process, the first step is to create an onboarding user record with email and phone number.

**Endpoint:** `POST /onboardinguser/`

**Payload:**
```typescript
{
  email: string;
  phone_no: string;
  DateOfCreation?: string;
  ModifiedDate?: string;
}
```

**Example Usage:**
```typescript
import { kycService } from '@/services/kycService';

const createOnboardingUser = async (email: string, phone: string) => {
  try {
    const response = await kycService.createOnboardingUser(email, phone);
    console.log('Onboarding user created:', response);
    return response;
  } catch (error) {
    console.error('Error creating onboarding user:', error);
    throw error;
  }
};
```

### 2. Email and Phone Verification

After creating the onboarding user, verify the email and phone number using OTP.

#### Send Email OTP
**Endpoint:** `POST /user/generate/otp/email`

```typescript
const sendEmailOTP = async (email: string) => {
  try {
    await kycService.sendKYCEmailOtp(email);
    console.log('Email OTP sent successfully');
  } catch (error) {
    console.error('Error sending email OTP:', error);
    throw error;
  }
};
```

#### Send Phone OTP
**Endpoint:** `POST /user/generate/otp/phone`

```typescript
const sendPhoneOTP = async (phone: string) => {
  try {
    await kycService.sendKYCPhoneOtp(phone);
    console.log('Phone OTP sent successfully');
  } catch (error) {
    console.error('Error sending phone OTP:', error);
    throw error;
  }
};
```

#### Verify Email OTP
**Endpoint:** `PUT /user/verify/email/`

```typescript
const verifyEmailOTP = async (email: string, otp: string) => {
  try {
    const isValid = await kycService.verifyKYCEmailOtp(email, otp);
    if (isValid) {
      console.log('Email verified successfully');
      return true;
    }
  } catch (error) {
    console.error('Error verifying email OTP:', error);
    throw error;
  }
};
```

#### Verify Phone OTP
**Endpoint:** `PUT /user/verify/phone/`

```typescript
const verifyPhoneOTP = async (phone: string, otp: string) => {
  try {
    const isValid = await kycService.verifyKYCPhoneOtp(phone, otp);
    if (isValid) {
      console.log('Phone verified successfully');
      return true;
    }
  } catch (error) {
    console.error('Error verifying phone OTP:', error);
    throw error;
  }
};
```

### 3. Complete KYC Information

After verification, proceed with the complete KYC form submission including personal information, address, and documents.

## CRUD Operations for Onboarding Users

### Get All Onboarding Users
```typescript
const getAllOnboardingUsers = async () => {
  try {
    const users = await kycService.getAllOnboardingUsers();
    return users;
  } catch (error) {
    console.error('Error fetching onboarding users:', error);
    throw error;
  }
};
```

### Get Onboarding User by ID
```typescript
const getOnboardingUserById = async (id: string) => {
  try {
    const user = await kycService.getOnboardingUserById(id);
    return user;
  } catch (error) {
    console.error('Error fetching onboarding user:', error);
    throw error;
  }
};
```

### Update Onboarding User
```typescript
const updateOnboardingUser = async (id: string, email: string, phone: string) => {
  try {
    const response = await kycService.updateOnboardingUser(id, email, phone);
    return response;
  } catch (error) {
    console.error('Error updating onboarding user:', error);
    throw error;
  }
};
```

### Delete Onboarding User
```typescript
const deleteOnboardingUser = async (id: string) => {
  try {
    const success = await kycService.deleteOnboardingUser(id);
    return success;
  } catch (error) {
    console.error('Error deleting onboarding user:', error);
    throw error;
  }
};
```

## Integration with React Components

### VerificationProcess Component

The `VerificationProcess` component handles the initial step where users enter email and phone numbers:

```typescript
const handleProceed = async () => {
  const isEmailValid = validateEmail(email);
  const isMobileValid = validateMobile(mobile);

  if (isEmailValid && isMobileValid) {
    setIsSubmitting(true);

    try {
      // Create onboarding user with email and phone
      const response = await kycService.createOnboardingUser(email, mobile);
      
      if (response.success) {
        // Update Redux store with user data
        dispatch(updatePersonalInfo({ 
          email, 
          mobile,
          onboardingUserId: response.id
        }));

        toast.success("Client information saved successfully");
        navigate("/kyc/contact-verification");
      }
    } catch (error) {
      toast.error("Failed to save client information");
    } finally {
      setIsSubmitting(false);
    }
  }
};
```

### ContactVerification Component

The `ContactVerification` component handles OTP verification:

```typescript
const handleVerifyEmail = async () => {
  if (emailOTP.length !== 6) {
    toast.error("Please enter a valid OTP");
    return;
  }

  setIsVerifyingEmail(true);
  try {
    const isValid = await kycService.verifyKYCEmailOtp(personalInfo.email, emailOTP);
    
    if (isValid) {
      setIsEmailVerified(true);
      dispatch(verifyEmail(true));
      toast.success("Email verified successfully");
      // Generate mobile OTP after email is verified
      await generateMobileOTP();
    }
  } catch (error) {
    toast.error("Invalid OTP. Please try again.");
  } finally {
    setIsVerifyingEmail(false);
  }
};
```

## Error Handling

All API methods include comprehensive error handling:

1. **Network Errors**: Connection issues, timeouts
2. **Authentication Errors**: Invalid or expired tokens
3. **Validation Errors**: Invalid email/phone format, missing required fields
4. **Server Errors**: Internal server errors, database issues

Example error handling pattern:
```typescript
try {
  const result = await kycService.createOnboardingUser(email, phone);
  // Handle success
} catch (error) {
  if (error instanceof Error) {
    // Display user-friendly error message
    toast.error(error.message);
  } else {
    // Fallback error message
    toast.error("An unexpected error occurred. Please try again.");
  }
}
```

## Best Practices

1. **Always validate input** before making API calls
2. **Use loading states** to provide user feedback
3. **Handle errors gracefully** with user-friendly messages
4. **Store onboarding user ID** for future reference
5. **Clear sensitive data** after successful verification
6. **Implement retry logic** for network failures
7. **Use proper TypeScript types** for type safety

## Security Considerations

1. **Never store OTP codes** in client-side storage
2. **Use HTTPS** for all API communications
3. **Validate tokens** before making authenticated requests
4. **Implement rate limiting** for OTP generation
5. **Clear session data** after completion
6. **Use secure headers** for API requests

## Testing

Test the onboarding flow with:

1. Valid email and phone combinations
2. Invalid email/phone formats
3. Network failure scenarios
4. Invalid OTP codes
5. Expired OTP codes
6. Authentication failures

This ensures a robust and reliable onboarding experience for users.
