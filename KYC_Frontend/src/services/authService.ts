import { User, LoginResponse, SignupResponse } from "../types/user";

// API configuration
export const API_CONFIG = {
  baseURL: "http://3.6.237.70:8001/v1/services",
  headers: {
    "Content-Type": "application/json",
  },
};

// Token storage using localStorage with expiry management
const setTokens = (accessToken: string, refreshToken: string) => {
  const expiryTime = Date.now() + 24 * 60 * 60 * 1000; // 24 hours from now

  localStorage.setItem("access_token", accessToken);
  localStorage.setItem("refresh_token", refreshToken);
  localStorage.setItem("token_expiry", expiryTime.toString());
};

const getAccessToken = () => {
  const token = localStorage.getItem("access_token");
  const expiry = localStorage.getItem("token_expiry");

  if (token && expiry) {
    const expiryTime = parseInt(expiry);
    if (Date.now() < expiryTime) {
      return token;
    } else {
      // Token expired, clear all auth data
      removeTokens();
      return null;
    }
  }
  return null;
};

const getRefreshToken = () => {
  const token = localStorage.getItem("refresh_token");
  const expiry = localStorage.getItem("token_expiry");

  if (token && expiry) {
    const expiryTime = parseInt(expiry);
    if (Date.now() < expiryTime) {
      return token;
    } else {
      // Token expired, clear all auth data
      removeTokens();
      return null;
    }
  }
  return null;
};

const removeTokens = () => {
  localStorage.removeItem("access_token");
  localStorage.removeItem("refresh_token");
  localStorage.removeItem("user_data");
  localStorage.removeItem("token_expiry");
};

// Check if tokens are valid and not expired
const areTokensValid = (): boolean => {
  const accessToken = localStorage.getItem("access_token");
  const refreshToken = localStorage.getItem("refresh_token");
  const expiry = localStorage.getItem("token_expiry");

  if (!accessToken || !refreshToken || !expiry) {
    return false;
  }

  const expiryTime = parseInt(expiry);
  return Date.now() < expiryTime;
};

// Check if tokens will expire soon (within 1 hour)
const willTokensExpireSoon = (): boolean => {
  const expiry = localStorage.getItem("token_expiry");

  if (!expiry) {
    return true;
  }

  const expiryTime = parseInt(expiry);
  const oneHourFromNow = Date.now() + 60 * 60 * 1000; // 1 hour
  return expiryTime < oneHourFromNow;
};

// Get time until token expiry in milliseconds
const getTimeUntilExpiry = (): number => {
  const expiry = localStorage.getItem("token_expiry");

  if (!expiry) {
    return 0;
  }

  const expiryTime = parseInt(expiry);
  return Math.max(0, expiryTime - Date.now());
};

// User data storage for authentication
const setAuthUserData = (userData: User) => {
  localStorage.setItem("user_data", JSON.stringify(userData));
};

const getAuthUserData = (): User | null => {
  const data = localStorage.getItem("user_data");
  return data ? JSON.parse(data) : null;
};

// User data storage for registration flow
const setUserData = (userData: User) => {
  sessionStorage.setItem("registration_user_data", JSON.stringify(userData));
};

const getUserData = (): User | null => {
  const data = sessionStorage.getItem("registration_user_data");
  return data ? JSON.parse(data) : null;
};

const removeUserData = () => {
  sessionStorage.removeItem("registration_user_data");
};

// Helper function to handle API errors
export const handleApiError = async (response: Response) => {
  if (!response.ok) {
    try {
      const error = await response.json();
      let errorMessage =
        error.message || error.error || error.detail || error.Description;

      if (!errorMessage) {
        // Provide user-friendly error messages based on status code
        if (response.status === 401) {
          errorMessage = "Authentication failed. Please login again.";
        } else if (response.status === 403) {
          errorMessage =
            "Access denied. You don't have permission for this action.";
        } else if (response.status === 404) {
          errorMessage = "The requested resource was not found.";
        } else if (response.status === 409) {
          errorMessage = "Conflict: The resource already exists.";
        } else if (response.status === 422) {
          errorMessage =
            "Invalid data provided. Please check your information.";
        } else if (response.status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else if (response.status >= 400 && response.status < 500) {
          errorMessage = "Invalid request. Please check your information.";
        } else {
          errorMessage = "Request failed. Please try again.";
        }
      }

      throw new Error(errorMessage);
    } catch (e) {
      // If response is not JSON, provide user-friendly error based on status
      let errorMessage: string;
      if (response.status === 401) {
        errorMessage = "Authentication failed. Please login again.";
      } else if (response.status === 403) {
        errorMessage =
          "Access denied. You don't have permission for this action.";
      } else if (response.status === 404) {
        errorMessage = "The requested resource was not found.";
      } else if (response.status === 500) {
        errorMessage = "Server error. Please try again later.";
      } else if (response.status >= 400 && response.status < 500) {
        errorMessage = "Invalid request. Please check your information.";
      } else {
        errorMessage =
          "Network error. Please check your connection and try again.";
      }
      throw new Error(errorMessage);
    }
  }
  try {
    return await response.json();
  } catch (e) {
    throw new Error("Invalid response from server");
  }
};

// Helper function to create fetch options
export const createFetchOptions = <T>(
  method: string,
  body?: T,
  includeAuth = false
): RequestInit => {
  const headers: Record<string, string> = { ...API_CONFIG.headers };

  if (includeAuth) {
    const token = getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  const options: RequestInit = {
    method,
    headers,
    credentials: "omit",
    mode: "cors",
  };

  // Only add body for methods that support it (not GET or HEAD)
  if (
    method.toUpperCase() !== "GET" &&
    method.toUpperCase() !== "HEAD" &&
    body
  ) {
    options.body = JSON.stringify(body);
  }

  return options;
};

// Helper function to create fetch options for multipart form data
const createMultipartFetchOptions = (
  method: string,
  formData: FormData,
  includeAuth = false
): RequestInit => {
  const headers: Record<string, string> = {};

  if (includeAuth) {
    const token = getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  // Don't set Content-Type header for FormData - browser will set it automatically with boundary

  return {
    method,
    headers,
    credentials: "omit",
    mode: "cors",
    body: formData,
  };
};

export const authService = {
  // Login user with email/phone and password
  login: async (
    identifier: string,
    password: string
  ): Promise<LoginResponse> => {
    // Based on the API response, the backend expects "PhoneOrEmail" field
    const body = {
      PhoneOrEmail: identifier,
      password: password,
    };

    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/login`,
        createFetchOptions("POST", body)
      );

      // Check if response is HTML (error page) or JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("text/html")) {
        // Server returned HTML error page
        throw new Error(`Server error: ${response.status} - Check server logs`);
      }

      let data: LoginResponse;
      try {
        data = await response.json();
      } catch (parseError) {
        throw new Error("Invalid response format from server");
      }

      if (response.ok && (data.IsSucess || data.status)) {
        // Store tokens and user data
        if (data.access_token && data.refresh_token) {
          setTokens(data.access_token, data.refresh_token);
        }
        if (data.user) {
          setAuthUserData(data.user);
        }
        return data;
      } else {
        // Create human-readable error messages
        let errorMessage = data.message || data.error || data.detail;

        if (!errorMessage) {
          // If no specific error message, provide user-friendly defaults
          if (response.status === 401) {
            errorMessage =
              "Invalid email/phone or password. Please check your credentials.";
          } else if (response.status === 404) {
            errorMessage =
              "Account not found. Please check your email/phone or register first.";
          } else if (response.status === 400) {
            errorMessage =
              "Invalid login information. Please check your email/phone and password.";
          } else if (response.status === 500) {
            errorMessage = "Server error. Please try again later.";
          } else if (response.ok) {
            // Status 200 but login failed - likely wrong credentials
            errorMessage =
              "Invalid email/phone or password. Please check your credentials.";
          } else {
            errorMessage =
              "Login failed. Please check your credentials and try again.";
          }
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error("Login failed");
    }
  },

  // Register user
  register: async (userData: User): Promise<SignupResponse> => {
    // Map the user data to match API expectations
    const signupData = {
      name:
        userData.name ||
        `${userData.firstName || ""} ${userData.lastName || ""}`.trim(),
      email: userData.email,
      phone_no: userData.phone_no,
      password: userData.password,
      gender: userData.gender || "Male", // Default gender if not provided
    };

    const response = await fetch(
      `${API_CONFIG.baseURL}/user/signup`,
      createFetchOptions("POST", signupData)
    );

    const data = await response.json();

    // If the response status is not ok, but we got JSON data, check for error in the data
    if (!response.ok) {
      let errorMessage = data.message || data.Description;

      if (!errorMessage) {
        // Provide user-friendly error messages based on status code
        if (response.status === 400) {
          errorMessage =
            "Invalid registration information. Please check your details and try again.";
        } else if (response.status === 409) {
          errorMessage =
            "An account with this email or phone number already exists.";
        } else if (response.status === 422) {
          errorMessage =
            "Please check your information. Some fields may be invalid.";
        } else if (response.status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage =
            "Registration failed. Please check your information and try again.";
        }
      }

      throw new Error(errorMessage);
    }

    return data;
  },

  // Send OTP for login
  sendLoginOtp: async (contact: string): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/generate/login/otp`,
      createFetchOptions("POST", { PhoneOrEmail: contact })
    );

    await handleApiError(response);
    return true;
  },

  // Send OTP for email verification
  sendEmailOtp: async (email: string): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/generate/otp/email`,
      createFetchOptions("POST", { email })
    );

    await handleApiError(response);
    return true;
  },

  // Send OTP for phone verification
  sendPhoneOtp: async (phone: string): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/generate/otp/phone`,
      createFetchOptions("POST", { phone_no: phone })
    );

    await handleApiError(response);
    return true;
  },

  // Send OTP (legacy method for backward compatibility)
  sendOtp: async (
    contact: string,
    type: "email" | "mobile"
  ): Promise<boolean> => {
    if (type === "email") {
      return authService.sendEmailOtp(contact);
    } else {
      return authService.sendPhoneOtp(contact);
    }
  },

  // Login with OTP
  loginWithOtp: async (
    contact: string,
    otp: string
  ): Promise<LoginResponse> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/login/otp`,
      createFetchOptions("POST", { PhoneOrEmail: contact, otp })
    );

    const data = await handleApiError(response);

    // Store tokens and user data
    if (data.access_token && data.refresh_token) {
      setTokens(data.access_token, data.refresh_token);
    }
    if (data.user) {
      setAuthUserData(data.user);
    }

    return data;
  },

  // Verify email OTP
  verifyEmailOtp: async (email: string, otp: string): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/verify/email/`,
      createFetchOptions("PUT", { email, otp })
    );

    await handleApiError(response);
    return true;
  },

  // Verify phone OTP
  verifyPhoneOtp: async (phone: string, otp: string): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/verify/phone/`,
      createFetchOptions("PUT", { phone, otp })
    );

    await handleApiError(response);
    return true;
  },

  // Verify OTP (legacy method for backward compatibility)
  verifyOtp: async (
    contact: string,
    otp: string,
    type: "email" | "mobile"
  ): Promise<boolean> => {
    if (type === "email") {
      return authService.verifyEmailOtp(contact, otp);
    } else {
      return authService.verifyPhoneOtp(contact, otp);
    }
  },

  // Reset password
  resetPassword: async (
    email: string,
    newPassword: string
  ): Promise<boolean> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/reset-password`,
      createFetchOptions("POST", { email, newPassword })
    );

    await handleApiError(response);
    return true;
  },

  // Update user profile
  updateProfile: async (
    userId: string,
    updates: Partial<User>,
    file?: File
  ): Promise<User> => {
    let response: Response;

    if (file) {
      // Use multipart form data when uploading files
      const formData = new FormData();

      // Add all update fields to form data
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Add the file
      formData.append("profile_pic", file);

      response = await fetch(
        `${API_CONFIG.baseURL}/user/update/${userId}`,
        createMultipartFetchOptions("PUT", formData, true)
      );
    } else {
      // Use JSON for text-only updates
      response = await fetch(
        `${API_CONFIG.baseURL}/user/update/${userId}`,
        createFetchOptions("PUT", updates, true) // Include auth token
      );
    }

    // Handle the response - the API returns 200 even for errors
    let data: any;
    try {
      data = await response.json();
    } catch (e) {
      throw new Error("Invalid response from server");
    }

    // Check if the operation was successful
    if (!response.ok) {
      // Handle HTTP errors
      const errorMessage =
        data.message ||
        data.error ||
        data.detail ||
        data.Description ||
        "Failed to update profile";
      throw new Error(errorMessage);
    } else if (data.IsSuccess === false) {
      // Handle API-level errors (200 status but operation failed)
      const errorMessage =
        data.Description ||
        data.message ||
        data.error ||
        "Failed to update profile";
      throw new Error(errorMessage);
    }

    // Return the user data - check if it's nested in 'user' property or direct
    return data.user || data;
  },

  // Get current user
  getCurrentUser: async (): Promise<User> => {
    const response = await fetch(
      `${API_CONFIG.baseURL}/user/me`,
      createFetchOptions("GET", undefined, true) // Include auth token
    );

    const data = await handleApiError(response);
    return data.user || data;
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/logout`,
        createFetchOptions("POST", {}, true) // Include auth token
      );

      await handleApiError(response);
    } catch (error) {
      // Even if logout fails on server, clear local tokens
      console.error("Logout error:", error);
    } finally {
      // Always clear local tokens
      removeTokens();
    }
  },

  // Get stored tokens
  getTokens: () => ({
    accessToken: getAccessToken(),
    refreshToken: getRefreshToken(),
  }),

  // Check if tokens are valid and not expired
  areTokensValid: () => areTokensValid(),

  // Check if tokens will expire soon
  willTokensExpireSoon: () => willTokensExpireSoon(),

  // Get time until token expiry
  getTimeUntilExpiry: () => getTimeUntilExpiry(),

  // Get stored user data
  getStoredUserData: () => getAuthUserData(),

  // Clear stored tokens
  clearTokens: () => {
    removeTokens();
  },

  // Clean up incomplete registration
  cleanupIncompleteRegistration: () => {
    // Clear any stored registration data
    removeUserData();
    // Clear Redux state would be handled by the component
  },

  // User data management for registration flow
  setRegistrationData: (userData: User) => {
    setUserData(userData);
  },

  getRegistrationData: (): User | null => {
    return getUserData();
  },

  clearRegistrationData: () => {
    removeUserData();
  },

  // Complete registration after OTP verification
  completeRegistration: async (userData: User): Promise<SignupResponse> => {
    // Map the user data to match API expectations
    const signupData = {
      name:
        userData.name ||
        `${userData.firstName || ""} ${userData.lastName || ""}`.trim(),
      email: userData.email,
      phone_no: userData.phone_no,
      password: userData.password,
      gender: userData.gender || "Male", // Default gender if not provided
    };

    const response = await fetch(
      `${API_CONFIG.baseURL}/user/signup`,
      createFetchOptions("POST", signupData)
    );

    const data = await response.json();

    // If the response status is not ok, but we got JSON data, check for error in the data
    if (!response.ok) {
      const errorMessage =
        data.message ||
        data.Description ||
        `Registration failed: ${response.status}`;
      throw new Error(errorMessage);
    }

    return data;
  },

  // Check if user exists by trying to register (will fail if user exists)
  checkUserExists: async (userData: User): Promise<boolean> => {
    try {
      const signupData = {
        name:
          userData.name ||
          `${userData.firstName || ""} ${userData.lastName || ""}`.trim(),
        email: userData.email,
        phone_no: userData.phone_no,
        password: userData.password,
        gender: userData.gender || "Male",
      };

      const response = await fetch(
        `${API_CONFIG.baseURL}/user/signup`,
        createFetchOptions("POST", signupData)
      );

      const data = await response.json();

      // If registration succeeds, user didn't exist before
      if (response.ok && (data.IsSucess || data.IsSuccess)) {
        return false; // User didn't exist, registration successful
      }

      // If registration fails due to existing user
      return true; // User already exists
    } catch (error) {
      return true; // Assume user exists on error
    }
  },

  // Upload profile picture
  uploadProfilePicture: async (userId: string, file: File): Promise<string> => {
    const formData = new FormData();
    formData.append("profile_pic", file);

    const response = await fetch(
      `${API_CONFIG.baseURL}/user/update/${userId}`,
      createMultipartFetchOptions("PUT", formData, true)
    );

    // Handle the response - the API returns 200 even for errors
    let data: any;
    try {
      data = await response.json();
    } catch (e) {
      throw new Error("Invalid response from server");
    }

    // Check if the operation was successful
    if (!response.ok) {
      // Handle HTTP errors
      const errorMessage =
        data.message ||
        data.error ||
        data.detail ||
        data.Description ||
        "Failed to upload profile picture";
      throw new Error(errorMessage);
    } else if (data.IsSuccess === false) {
      // Handle API-level errors (200 status but operation failed)
      const errorMessage =
        data.Description ||
        data.message ||
        data.error ||
        "Failed to upload profile picture";
      throw new Error(errorMessage);
    }

    // The API returns the updated user object with profile_pic field
    // Try different possible response structures
    let profilePicUrl = "";

    if (data.profile_pic) {
      profilePicUrl = data.profile_pic;
    } else if (data.user?.profile_pic) {
      profilePicUrl = data.user.profile_pic;
    } else if (data.profile_pic_url) {
      profilePicUrl = data.profile_pic_url;
    } else if (data.user?.profile_pic_url) {
      profilePicUrl = data.user.profile_pic_url;
    }

    // Ensure the URL is absolute
    if (profilePicUrl && !profilePicUrl.startsWith("http")) {
      // Handle different possible URL formats
      if (profilePicUrl.startsWith("/")) {
        // Absolute path from root
        profilePicUrl = `${API_CONFIG.baseURL.replace(
          "/v1/services",
          ""
        )}${profilePicUrl}`;
      } else {
        // Relative path
        profilePicUrl = `${API_CONFIG.baseURL.replace(
          "/v1/services",
          ""
        )}/${profilePicUrl}`;
      }
    }

    return profilePicUrl;
  },
};
