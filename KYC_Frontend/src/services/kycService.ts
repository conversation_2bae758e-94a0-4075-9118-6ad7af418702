import { API_CONFIG, createFetchOptions, handleApiError } from "./authService";
import { PersonalInfo, AdditionalInfo } from "@/redux/slices/kycSlice";
import { withErrorHandling } from "@/utils/errorHandling";
import {
  retryAuthenticatedApiCall,
  categorizeError,
  DEFAULT_RETRY_CONFIG,
} from "@/utils/apiRetry";

// Types for KYC API
export interface OnboardingUserPayload {
  user_id: string;
  email: string;
  phone_no: string;
  isEmailverified: boolean;
  isphoneverified: boolean;
  isProfilePicVerified: boolean;
  DateOfCreation?: string;
  ModifiedDate?: string;
  // Additional possible ID fields that might be returned from API
  id?: string;
  Id?: string;
  ID?: string;
  userId?: string;
  User_id?: string;
}

export interface OnboardingUserResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    email: string;
    phone_no: string;
    DateOfCreation: string;
    ModifiedDate: string;
  };
  id?: string;
}

// Unified payload for Personal + Additional Information
export interface PersonalInformationPayload {
  id?: string;
  name: string;
  date_of_birth: string;
  gender: string;
  father_name: string;
  mother_name: string;
  martial_status: boolean;
  spouse_name?: string;
  no_of_dependent: number;
  name_of_guardian?: string;
  relation_with_gurdian?: string;
  nationality: string;
  country_name: string;
  citizenship: string;
  religion: string;
  persion_with_disability: boolean;
  education: string;
  occupation: string;
  organisation_name: string;
  designation_profession: string;
  nature_of_bussiness: string;
  net_worth: number;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface AddressInformationPayload {
  id?: string;
  address_type: string;
  house_no?: string;
  street: string;
  city: string;
  state: string;
  country: string;
  pincode: number; // Changed to number to match API model
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface DocumentInformationPayload {
  id?: string;
  document_id?: number; // readOnly, returned from API
  document_type: string;
  document?: File | string; // readOnly, returned from API as URL
  document_file?: File | string; // for upload only
  is_verrified?: boolean;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface KYCSubmissionResponse {
  success: boolean;
  message: string;
  data?: unknown;
  id?: string;
}

export interface KYCApplication {
  id: string;
  clientName: string;
  status: "pending" | "approved" | "rejected" | "in_progress";
  submissionDate: string;
  personalInfo: PersonalInfo;
  additionalInfo: AdditionalInfo;
  verificationStatus: {
    email: boolean;
    mobile: boolean;
    facial: boolean;
  };
}

// Helper function to map Personal Information form data to API payload
const mapPersonalInfoToPayload = (
  personalInfo: PersonalInfo
): Partial<PersonalInformationPayload> => {
  const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`.trim();
  const now = new Date().toISOString();

  return {
    name: fullName,
    date_of_birth: personalInfo.dob,
    gender: personalInfo.gender,
    father_name: personalInfo.fatherName,
    mother_name: personalInfo.motherName,
    martial_status: personalInfo.isMarried,
    spouse_name: personalInfo.spouseName || "",
    no_of_dependent: personalInfo.dependents,
    name_of_guardian: personalInfo.guardianName || "",
    relation_with_gurdian: personalInfo.guardianRelation || "",
    DateOfCreation: now,
    ModifiedDate: now,
  };
};

// Helper function to map Additional Information form data to API payload
export interface AdditionalInformationPayload {
  id?: string;
  nationality: string;
  country_name: string;
  citizenship: string;
  religion: string;
  persion_with_disability: boolean;
  education: string;
  occupation: string;
  organisation_name: string;
  designation_profession: string;
  nature_of_bussiness: string;
  net_worth: number;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

const mapAdditionalInfoToPayload = (
  additionalInfo: AdditionalInfo
): AdditionalInformationPayload => {
  const now = new Date().toISOString();

  return {
    nationality: additionalInfo.nationality,
    country_name: additionalInfo.country,
    citizenship: additionalInfo.citizenship,
    religion: additionalInfo.religion,
    persion_with_disability: additionalInfo.hasDisability,
    education: additionalInfo.education,
    occupation: additionalInfo.occupation,
    organisation_name: additionalInfo.organization,
    designation_profession: additionalInfo.designation,
    nature_of_bussiness: additionalInfo.businessNature,
    net_worth: parseInt(additionalInfo.netWorth) || 0,
    DateOfCreation: now,
    ModifiedDate: now,
  };
};

// Helper function to map combined form data to API payload (for backward compatibility)
const mapFormDataToPayload = (
  personalInfo: PersonalInfo,
  additionalInfo: AdditionalInfo
): PersonalInformationPayload => {
  const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`.trim();
  const now = new Date().toISOString();

  return {
    name: fullName,
    date_of_birth: personalInfo.dob,
    gender: personalInfo.gender,
    father_name: personalInfo.fatherName,
    mother_name: personalInfo.motherName,
    martial_status: personalInfo.isMarried,
    spouse_name: personalInfo.spouseName || "",
    no_of_dependent: personalInfo.dependents,
    name_of_guardian: personalInfo.guardianName || "",
    relation_with_gurdian: personalInfo.guardianRelation || "",
    nationality: additionalInfo.nationality,
    country_name: additionalInfo.country,
    citizenship: additionalInfo.citizenship,
    religion: additionalInfo.religion,
    persion_with_disability: additionalInfo.hasDisability,
    education: additionalInfo.education,
    occupation: additionalInfo.occupation,
    organisation_name: additionalInfo.organization,
    designation_profession: additionalInfo.designation,
    nature_of_bussiness: additionalInfo.businessNature,
    net_worth: parseInt(additionalInfo.netWorth) || 0,
    DateOfCreation: now,
    ModifiedDate: now,
  };
};

// Define a type for address details
type AddressDetails = {
  temporary: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  permanent: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
};

// Helper function to map address data to API payload
const mapAddressDataToPayload = (
  addressType: "temporary" | "permanent",
  addressData: {
    house_no?: string;
    street: string;
    city: string;
    state: string;
    country: string;
    pincode: string | number;
  }
): AddressInformationPayload => {
  const now = new Date().toISOString();
  return {
    address_type: addressType,
    house_no: addressData.house_no || "",
    street: addressData.street,
    city: addressData.city,
    state: addressData.state,
    country: addressData.country,
    pincode:
      typeof addressData.pincode === "string"
        ? parseInt(addressData.pincode)
        : addressData.pincode,
    DateOfCreation: now,
    ModifiedDate: now,
  };
};

export const kycService = {
  // Check if email already exists in onboarding users
  checkEmailExists: async (email: string): Promise<boolean> => {
    try {
      // Get all onboarding users to check for duplicates
      const users = await kycService.getAllOnboardingUsers("", 1, 1000); // Get a large number to check all
      return users.some((user: OnboardingUserPayload) => user.email === email);
    } catch (error) {
      console.error("Error checking email existence:", error);
      // If we can't check, assume it doesn't exist to allow the operation
      return false;
    }
  },

  // Check if phone number already exists in onboarding users
  checkPhoneExists: async (phone_no: string): Promise<boolean> => {
    try {
      // Get all onboarding users to check for duplicates
      const users = await kycService.getAllOnboardingUsers("", 1, 1000); // Get a large number to check all
      return users.some(
        (user: { phone_no: string }) => user.phone_no === phone_no
      );
    } catch (error) {
      console.error("Error checking phone existence:", error);
      // If we can't check, assume it doesn't exist to allow the operation
      return false;
    }
  },

  // Check if email or phone already exists
  checkDuplicateContact: async (
    email: string,
    phone_no: string
  ): Promise<{
    emailExists: boolean;
    phoneExists: boolean;
    hasConflict: boolean;
  }> => {
    try {
      const [emailExists, phoneExists] = await Promise.all([
        kycService.checkEmailExists(email),
        kycService.checkPhoneExists(phone_no),
      ]);

      return {
        emailExists,
        phoneExists,
        hasConflict: emailExists || phoneExists,
      };
    } catch (error) {
      console.error("Error checking duplicate contacts:", error);
      return {
        emailExists: false,
        phoneExists: false,
        hasConflict: false,
      };
    }
  },

  // Create initial onboarding user with email and phone
  createOnboardingUser: async (
    email: string,
    phone_no: string,
    user_id: string
  ): Promise<OnboardingUserResponse> => {
    return (
      withErrorHandling(
        async () => {
          // Check for duplicate email and phone before creating
          const duplicateCheck = await kycService.checkDuplicateContact(
            email,
            phone_no
          );

          if (duplicateCheck.hasConflict) {
            const errors = [];
            if (duplicateCheck.emailExists) {
              errors.push(`Email "${email}" is already registered`);
            }
            if (duplicateCheck.phoneExists) {
              errors.push(`Phone number "${phone_no}" is already registered`);
            }
            throw new Error(errors.join(". "));
          }

          const now = new Date().toISOString();
          const payload: OnboardingUserPayload = {
            user_id: user_id,
            email: email,
            phone_no: phone_no,
            isEmailverified: false,
            isphoneverified: false,
            isProfilePicVerified: false,
            DateOfCreation: now,
            ModifiedDate: now,
          };
          console.log("Payload - ", payload);
          const response = await fetch(
            `${API_CONFIG.baseURL}/onboardinguser/information/`,
            createFetchOptions("POST", payload, true) // Include auth token
          );

          const data = await handleApiError(response);

          return {
            success: true,
            message: "Onboarding user created successfully",
            data: data,
            id: data.id || data.Id || data.ID,
          };
        },
        {
          operation: "createOnboardingUser",
          email,
          phone_no,
        }
      ) || Promise.reject(new Error("Failed to create onboarding user"))
    );
  },

  // Get all onboarding users
  getAllOnboardingUsers: async (
    user_id: string,
    page_no: number,
    page_length: number
  ): Promise<OnboardingUserPayload[]> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/?user=${user_id}&page=${page_no}&length=${page_length}`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);

      // Handle different response formats
      if (data && data.onboarding_user && Array.isArray(data.onboarding_user)) {
        return data.onboarding_user;
      } else if (Array.isArray(data)) {
        return data;
      } else if (data && typeof data === "object") {
        return [data];
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error fetching onboarding users:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch onboarding users");
    }
  },

  // Get specific onboarding user by ID
  getOnboardingUserById: async (id: string): Promise<OnboardingUserPayload> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching onboarding user by ID:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch onboarding user");
    }
  },

  // Update onboarding user by ID
  updateOnboardingUser: async (
    id: string,
    updates: Partial<OnboardingUserPayload>
  ): Promise<OnboardingUserResponse> => {
    try {
      const now = new Date().toISOString();
      const payload = {
        ...updates,
        ModifiedDate: now,
      };

      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/${id}/`,
        createFetchOptions("PUT", payload, true) // Include auth token
      );

      const data = await handleApiError(response);

      return {
        success: true,
        message: "Onboarding user updated successfully",
        data: data,
        id: id,
      };
    } catch (error) {
      console.error("Onboarding user update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update onboarding user");
    }
  },

  // Update onboarding user verification status
  updateOnboardingUserVerification: async (
    email: string,
    phone_no: string,
    isEmailverified?: boolean,
    isphoneverified?: boolean
  ): Promise<OnboardingUserResponse> => {
    try {
      // First, find the onboarding user by email and phone
      const users = await kycService.getAllOnboardingUsers("", 1, 100); // Get all users to find the right one
      const user = users.find(
        (u: OnboardingUserPayload) =>
          u.email === email && u.phone_no === phone_no
      );

      if (!user) {
        throw new Error("Onboarding user not found");
      }

      const updates: Partial<OnboardingUserPayload> = {};
      if (isEmailverified !== undefined) {
        updates.isEmailverified = isEmailverified;
      }
      if (isphoneverified !== undefined) {
        updates.isphoneverified = isphoneverified;
      }

      return await kycService.updateOnboardingUser(user.user_id, updates);
    } catch (error) {
      console.error("Error updating onboarding user verification:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update onboarding user verification");
    }
  },

  // Update onboarding user verification by contact (email or phone)
  updateOnboardingUserVerificationByContact: async (
    contact: string,
    type: "email" | "mobile",
    verified: boolean
  ): Promise<OnboardingUserResponse> => {
    try {
      // First, find the onboarding user by contact
      const users = await kycService.getAllOnboardingUsers("", 1, 100); // Get all users to find the right one
      const user = users.find((u: OnboardingUserPayload) => {
        if (type === "email") {
          return u.email === contact;
        } else {
          return u.phone_no === contact;
        }
      });

      if (!user) {
        throw new Error(`Onboarding user not found for ${type}: ${contact}`);
      }

      const updates: Partial<OnboardingUserPayload> = {};
      if (type === "email") {
        updates.isEmailverified = verified;
      } else {
        updates.isphoneverified = verified;
      }

      return await kycService.updateOnboardingUser(user.user_id, updates);
    } catch (error) {
      console.error(
        "Error updating onboarding user verification by contact:",
        error
      );
      throw error instanceof Error
        ? error
        : new Error("Failed to update onboarding user verification");
    }
  },

  // Delete onboarding user by ID
  deleteOnboardingUser: async (id: string): Promise<boolean> => {
    try {
      // Try the information endpoint pattern first (consistent with other operations)
      let response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      // If that fails, try the direct endpoint pattern
      if (!response.ok && response.status === 404) {
        console.log("Trying alternative delete endpoint...");
        response = await fetch(
          `${API_CONFIG.baseURL}/onboardinguser/${id}/`,
          createFetchOptions("DELETE", undefined, true) // Include auth token
        );
      }

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting onboarding user:", error);
      console.error("Failed ID:", id);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete onboarding user");
    }
  },

  // Send OTP for email verification (KYC specific)
  sendKYCEmailOtp: async (email: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/generate/otp/email`,
        createFetchOptions("POST", { email }, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error sending email OTP:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to send email OTP");
    }
  },

  // Send OTP for phone verification (KYC specific)
  sendKYCPhoneOtp: async (phone_no: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/generate/otp/phone`,
        createFetchOptions("POST", { phone_no }, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error sending phone OTP:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to send phone OTP");
    }
  },

  // Verify email OTP (KYC specific)
  verifyKYCEmailOtp: async (email: string, otp: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/verify/email/`,
        createFetchOptions("PUT", { email, otp }, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error verifying email OTP:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to verify email OTP");
    }
  },

  // Verify phone OTP (KYC specific)
  verifyKYCPhoneOtp: async (phone: string, otp: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/user/verify/phone/`,
        createFetchOptions("PUT", { phone, otp }, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error verifying phone OTP:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to verify phone OTP");
    }
  },

  // Submit Personal Information only
  submitPersonalInformationOnly: async (
    personalInfo: PersonalInfo
  ): Promise<KYCSubmissionResponse> => {
    return (
      withErrorHandling(
        async () => {
          const payload = mapPersonalInfoToPayload(personalInfo);

          const response = await fetch(
            `${API_CONFIG.baseURL}/onboardinguser/information/personal/`,
            createFetchOptions("POST", payload, true) // Include auth token
          );

          const data = await handleApiError(response);

          return {
            success: true,
            message: "Personal information submitted successfully",
            data: data,
            id: data.id || data.Id || data.ID,
          };
        },
        {
          operation: "submitPersonalInformationOnly",
          personalInfo: {
            firstName: personalInfo.firstName,
            lastName: personalInfo.lastName,
          },
        }
      ) || Promise.reject(new Error("Failed to submit personal information"))
    );
  },

  // Submit Additional Information only
  submitAdditionalInformationOnly: async (
    additionalInfo: AdditionalInfo
  ): Promise<KYCSubmissionResponse> => {
    return (
      withErrorHandling(
        async () => {
          const payload = mapAdditionalInfoToPayload(additionalInfo);

          const response = await fetch(
            `${API_CONFIG.baseURL}/onboardinguser/information/additional/`,
            createFetchOptions("POST", payload, true) // Include auth token
          );

          const data = await handleApiError(response);

          return {
            success: true,
            message: "Additional information submitted successfully",
            data: data,
            id: data.id || data.Id || data.ID,
          };
        },
        {
          operation: "submitAdditionalInformationOnly",
          additionalInfo: {
            nationality: additionalInfo.nationality,
            education: additionalInfo.education,
          },
        }
      ) || Promise.reject(new Error("Failed to submit additional information"))
    );
  },

  // Get all personal information records
  getAllPersonalInformation: async (): Promise<
    PersonalInformationPayload[]
  > => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/personal/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error("Error fetching personal information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch personal information");
    }
  },

  // Get all additional information records
  getAllAdditionalInformation: async (): Promise<
    AdditionalInformationPayload[]
  > => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/additional/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error("Error fetching additional information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch additional information");
    }
  },

  // Get specific personal information by ID
  getPersonalInformationById: async (
    id: string
  ): Promise<PersonalInformationPayload> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/personal/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching personal information by ID:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch personal information");
    }
  },

  // Get specific additional information by ID
  getAdditionalInformationById: async (
    id: string
  ): Promise<AdditionalInformationPayload> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/additional/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching additional information by ID:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch additional information");
    }
  },

  // Update personal information by ID
  updatePersonalInformationOnly: async (
    id: string,
    personalInfo: PersonalInfo
  ): Promise<KYCSubmissionResponse> => {
    try {
      const payload = mapPersonalInfoToPayload(personalInfo);

      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/personal/${id}/`,
        createFetchOptions("PUT", payload, true) // Include auth token
      );

      const data = await handleApiError(response);

      return {
        success: true,
        message: "Personal information updated successfully",
        data: data,
        id: id,
      };
    } catch (error) {
      console.error("Personal information update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update personal information");
    }
  },

  // Update additional information by ID
  updateAdditionalInformationOnly: async (
    id: string,
    additionalInfo: AdditionalInfo
  ): Promise<KYCSubmissionResponse> => {
    try {
      const payload = mapAdditionalInfoToPayload(additionalInfo);

      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/additional/${id}/`,
        createFetchOptions("PUT", payload, true) // Include auth token
      );

      const data = await handleApiError(response);

      return {
        success: true,
        message: "Additional information updated successfully",
        data: data,
        id: id,
      };
    } catch (error) {
      console.error("Additional information update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update additional information");
    }
  },

  // Submit personal information (combined personal + additional info) - for backward compatibility
  submitPersonalInformation: async (
    personalInfo: PersonalInfo,
    additionalInfo: AdditionalInfo
  ): Promise<KYCSubmissionResponse> => {
    return (
      withErrorHandling(
        async () => {
          const payload = mapFormDataToPayload(personalInfo, additionalInfo);

          const response = await fetch(
            `${API_CONFIG.baseURL}/onboardinguser/information/personal/`,
            createFetchOptions("POST", payload, true) // Unified endpoint
          );

          const data = await handleApiError(response);

          return {
            success: true,
            message: "Personal information submitted successfully",
            data: data,
            id: data.id || data.Id || data.ID,
          };
        },
        {
          operation: "submitPersonalInformation",
          personalInfo: {
            firstName: personalInfo.firstName,
            lastName: personalInfo.lastName,
          },
        }
      ) || Promise.reject(new Error("Failed to submit personal information"))
    );
  },

  // Delete personal information by ID
  deletePersonalInformation: async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/personal/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting personal information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete personal information");
    }
  },

  // Delete additional information by ID
  deleteAdditionalInformation: async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/additional/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting additional information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete additional information");
    }
  },

  // Get KYC applications for the current user
  getKYCApplications: async (): Promise<KYCApplication[]> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);

      // Transform API response to our application format
      // This is a placeholder - adjust based on actual API response structure
      return data.applications || [];
    } catch (error) {
      console.error("Error fetching KYC applications:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch KYC applications");
    }
  },

  // Get specific KYC application by ID
  getKYCApplication: async (id: string): Promise<KYCApplication> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching KYC application:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch KYC application");
    }
  },

  // Update KYC application
  updateKYCApplication: async (
    id: string,
    personalInfo: PersonalInfo,
    additionalInfo: AdditionalInfo
  ): Promise<KYCSubmissionResponse> => {
    try {
      const payload = mapFormDataToPayload(personalInfo, additionalInfo);

      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/personal/${id}/`,
        createFetchOptions("PUT", payload, true) // Include auth token
      );

      const data = await handleApiError(response);

      return {
        success: true,
        message: "KYC application updated successfully",
        data: data,
        id: id,
      };
    } catch (error) {
      console.error("KYC update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update KYC application");
    }
  },

  // Delete KYC application
  deleteKYCApplication: async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting KYC application:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete KYC application");
    }
  },

  // Get all address information records
  getAllAddressInformation: async (): Promise<AddressInformationPayload[]> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error("Error fetching address information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch address information");
    }
  },

  // Get specific address information by ID
  getAddressInformationById: async (
    id: string
  ): Promise<AddressInformationPayload> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching address information by ID:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch address information");
    }
  },

  // Submit address information with retry mechanism
  submitAddressInformation: async (addressData: {
    temporary: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country?: string;
    };
    permanent: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country?: string;
    };
  }): Promise<KYCSubmissionResponse> => {
    // Validate input data
    if (
      !addressData.temporary.street ||
      !addressData.temporary.city ||
      !addressData.temporary.state ||
      !addressData.temporary.pincode
    ) {
      throw new Error("Temporary address information is incomplete");
    }
    if (
      !addressData.permanent.street ||
      !addressData.permanent.city ||
      !addressData.permanent.state ||
      !addressData.permanent.pincode
    ) {
      throw new Error("Permanent address information is incomplete");
    }

    // Create payloads for both temporary and permanent addresses
    const temporaryPayload = mapAddressDataToPayload("temporary", {
      ...addressData.temporary,
      country: addressData.temporary.country || "India", // Provide a default or fetch from user input
    });
    const permanentPayload = mapAddressDataToPayload("permanent", {
      ...addressData.permanent,
      country: addressData.permanent.country || "India", // Provide a default or fetch from user input
    });

    const apiCall = async (): Promise<KYCSubmissionResponse> => {
      console.log("Submitting temporary address:", temporaryPayload);
      console.log("Submitting permanent address:", permanentPayload);

      // Submit temporary address first
      const tempResponse = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/`,
        createFetchOptions("POST", temporaryPayload, true)
      );
      const tempData = await handleApiError(tempResponse);
      console.log("Temporary address response:", tempData);

      // Submit permanent address
      const permResponse = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/`,
        createFetchOptions("POST", permanentPayload, true)
      );
      const permData = await handleApiError(permResponse);
      console.log("Permanent address response:", permData);

      return {
        success: true,
        message: "Address information submitted successfully",
        data: {
          temporary: tempData,
          permanent: permData,
        },
        id: `${tempData.id || tempData.Id || tempData.ID}_${
          permData.id || permData.Id || permData.ID
        }`,
      };
    };

    const refreshAuth = async (): Promise<boolean> => {
      // For now, return false as we don't have token refresh implemented
      // This can be enhanced later with actual token refresh logic
      return false;
    };

    const result = await retryAuthenticatedApiCall(
      apiCall,
      refreshAuth,
      DEFAULT_RETRY_CONFIG,
      "submitAddressInformation"
    );

    if (result.success && result.data) {
      return result.data;
    } else {
      const errorInfo = categorizeError(
        result.error || new Error("Unknown error")
      );
      throw new Error(errorInfo.userMessage);
    }
  },

  // Update address information by ID
  updateAddressInformation: async (
    temporaryId: string,
    permanentId: string,
    addressData: {
      temporary: {
        street: string;
        city: string;
        state: string;
        pincode: string;
        country?: string;
      };
      permanent: {
        street: string;
        city: string;
        state: string;
        pincode: string;
        country?: string;
      };
    }
  ): Promise<KYCSubmissionResponse> => {
    try {
      const temporaryPayload = mapAddressDataToPayload("temporary", {
        ...addressData.temporary,
        country: addressData.temporary.country || "India",
      });
      const permanentPayload = mapAddressDataToPayload("permanent", {
        ...addressData.permanent,
        country: addressData.permanent.country || "India",
      });

      // Update temporary address
      const tempResponse = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/${temporaryId}/`,
        createFetchOptions("PUT", temporaryPayload, true)
      );
      const tempData = await handleApiError(tempResponse);

      // Update permanent address
      const permResponse = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/${permanentId}/`,
        createFetchOptions("PUT", permanentPayload, true)
      );
      const permData = await handleApiError(permResponse);

      return {
        success: true,
        message: "Address information updated successfully",
        data: {
          temporary: tempData,
          permanent: permData,
        },
        id: `${temporaryId}_${permanentId}`,
      };
    } catch (error) {
      console.error("Address update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update address information");
    }
  },

  // Delete address information by ID
  deleteAddressInformation: async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/address/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting address information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete address information");
    }
  },

  // Get all document information records
  getAllDocumentInformation: async (): Promise<
    DocumentInformationPayload[]
  > => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/documents/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error("Error fetching document information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch document information");
    }
  },

  // Get specific document information by ID
  getDocumentInformationById: async (
    id: string
  ): Promise<DocumentInformationPayload> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/documents/${id}/`,
        createFetchOptions("GET", undefined, true) // Include auth token
      );

      const data = await handleApiError(response);
      return data;
    } catch (error) {
      console.error("Error fetching document information by ID:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to fetch document information");
    }
  },

  // Submit document information with retry mechanism
  submitDocumentInformation: async (
    documentData: DocumentInformationPayload
  ): Promise<KYCSubmissionResponse> => {
    if (!documentData.document_type) {
      throw new Error("Document type is required");
    }
    if (!documentData.document_file) {
      throw new Error("Document file is required");
    }

    const apiCall = async (): Promise<KYCSubmissionResponse> => {
      let response: Response;

      if (documentData.document_file instanceof File) {
        const formData = new FormData();
        formData.append("document_type", documentData.document_type);
        formData.append("document_file", documentData.document_file); // Use document_file for upload
        formData.append(
          "is_verrified",
          (documentData.is_verrified || false).toString()
        );
        const now = new Date().toISOString();
        formData.append("DateOfCreation", now);
        formData.append("ModifiedDate", now);

        const token = localStorage.getItem("access_token");
        if (!token) {
          throw new Error(
            "Authentication required. Please log in to continue."
          );
        }

        response = await fetch(
          `${API_CONFIG.baseURL}/onboardinguser/information/documents/`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
            },
            body: formData,
          }
        );
      } else {
        const payload = {
          document_type: documentData.document_type,
          is_verrified: documentData.is_verrified || false,
          DateOfCreation: new Date().toISOString(),
          ModifiedDate: new Date().toISOString(),
        };

        response = await fetch(
          `${API_CONFIG.baseURL}/onboardinguser/information/documents/`,
          createFetchOptions("POST", payload, true)
        );
      }

      const data = await handleApiError(response);

      return {
        success: true,
        message: "Document information submitted successfully",
        data: data,
        id: data.id || data.Id || data.ID,
      };
    };

    const refreshAuth = async (): Promise<boolean> => {
      // For now, return false as we don't have token refresh implemented
      // This can be enhanced later with actual token refresh logic
      return false;
    };

    const result = await retryAuthenticatedApiCall(
      apiCall,
      refreshAuth,
      DEFAULT_RETRY_CONFIG,
      "submitDocumentInformation"
    );

    if (result.success && result.data) {
      return result.data;
    } else {
      const errorInfo = categorizeError(
        result.error || new Error("Unknown error")
      );
      throw new Error(errorInfo.userMessage);
    }
  },

  // Update document information by ID
  updateDocumentInformation: async (
    id: string,
    documentData: DocumentInformationPayload
  ): Promise<KYCSubmissionResponse> => {
    try {
      // Handle file upload if document contains files
      let response: Response;

      if (documentData.document_file instanceof File) {
        // Use FormData for file uploads
        const formData = new FormData();
        formData.append("document_type", documentData.document_type);
        formData.append("document", documentData.document_file); // Changed from document_file to document

        // Add verification status
        formData.append(
          "is_verrified",
          (documentData.is_verrified || false).toString()
        );

        // Add timestamps
        formData.append("ModifiedDate", new Date().toISOString());

        // Get token using the proper method
        const token = localStorage.getItem("access_token");
        if (!token) {
          throw new Error(
            "Authentication required. Please log in to continue."
          );
        }

        response = await fetch(
          `${API_CONFIG.baseURL}/onboardinguser/information/documents/${id}/`,
          {
            method: "PUT",
            headers: {
              Authorization: `Bearer ${token}`,
            },
            body: formData,
          }
        );
      } else {
        // Use JSON for non-file data
        const payload = {
          document_type: documentData.document_type,
          is_verrified: documentData.is_verrified || false,
          ModifiedDate: new Date().toISOString(),
        };

        response = await fetch(
          `${API_CONFIG.baseURL}/onboardinguser/information/documents/${id}/`,
          createFetchOptions("PUT", payload, true) // Include auth token
        );
      }

      const data = await handleApiError(response);

      return {
        success: true,
        message: "Document information updated successfully",
        data: data,
        id: id,
      };
    } catch (error) {
      console.error("Document update error:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to update document information");
    }
  },

  // Delete document information by ID
  deleteDocumentInformation: async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(
        `${API_CONFIG.baseURL}/onboardinguser/information/documents/${id}/`,
        createFetchOptions("DELETE", undefined, true) // Include auth token
      );

      await handleApiError(response);
      return true;
    } catch (error) {
      console.error("Error deleting document information:", error);
      throw error instanceof Error
        ? error
        : new Error("Failed to delete document information");
    }
  },
};
