// Enhanced API retry utility with exponential backoff and error categorization

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableStatusCodes: number[];
  retryableErrors: string[];
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
}

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
  retryableStatusCodes: [408, 429, 500, 502, 503, 504], // Timeout, Rate limit, Server errors
  retryableErrors: [
    'network error',
    'fetch failed',
    'failed to fetch',
    'connection refused',
    'timeout',
    'network request failed',
    'load failed',
  ],
};

// Check if an error is retryable
export function isRetryableError(error: Error, statusCode?: number): boolean {
  const errorMessage = error.message.toLowerCase();
  
  // Check if it's a retryable HTTP status code
  if (statusCode && DEFAULT_RETRY_CONFIG.retryableStatusCodes.includes(statusCode)) {
    return true;
  }
  
  // Check if it's a retryable error message
  return DEFAULT_RETRY_CONFIG.retryableErrors.some(retryableError =>
    errorMessage.includes(retryableError)
  );
}

// Check if an error is an authentication error
export function isAuthError(error: Error, statusCode?: number): boolean {
  if (statusCode === 401 || statusCode === 403) {
    return true;
  }
  
  const errorMessage = error.message.toLowerCase();
  const authErrors = [
    'authentication',
    'unauthorized',
    'forbidden',
    'access denied',
    'invalid token',
    'token expired',
    'credentials',
  ];
  
  return authErrors.some(authError => errorMessage.includes(authError));
}

// Calculate delay with exponential backoff and jitter
function calculateDelay(attempt: number, config: RetryConfig): number {
  const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
  const jitter = Math.random() * 0.1 * exponentialDelay; // Add 10% jitter
  const delay = Math.min(exponentialDelay + jitter, config.maxDelay);
  return Math.floor(delay);
}

// Sleep utility
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Enhanced retry function with detailed logging and error categorization
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  context?: string
): Promise<RetryResult<T>> {
  const startTime = Date.now();
  let lastError: Error;
  let attempts = 0;
  
  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    attempts = attempt;
    
    try {
      console.log(`🔄 API Call Attempt ${attempt}${context ? ` (${context})` : ''}`);
      const result = await apiCall();
      
      const totalTime = Date.now() - startTime;
      console.log(`✅ API Call Success on attempt ${attempt} (${totalTime}ms)${context ? ` (${context})` : ''}`);
      
      return {
        success: true,
        data: result,
        attempts,
        totalTime,
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      const totalTime = Date.now() - startTime;
      
      console.log(`❌ API Call Failed on attempt ${attempt}: ${lastError.message}${context ? ` (${context})` : ''}`);
      
      // Check if this is the last attempt
      if (attempt === config.maxRetries + 1) {
        console.log(`🚫 Max retries (${config.maxRetries}) exceeded${context ? ` (${context})` : ''}`);
        break;
      }
      
      // Check if error is retryable
      const statusCode = (error as any)?.status || (error as any)?.statusCode;
      
      if (isAuthError(lastError, statusCode)) {
        console.log(`🔐 Authentication error detected - not retrying${context ? ` (${context})` : ''}`);
        break;
      }
      
      if (!isRetryableError(lastError, statusCode)) {
        console.log(`🚫 Non-retryable error detected - not retrying${context ? ` (${context})` : ''}`);
        break;
      }
      
      // Calculate delay and wait
      const delay = calculateDelay(attempt, config);
      console.log(`⏳ Waiting ${delay}ms before retry ${attempt + 1}${context ? ` (${context})` : ''}`);
      await sleep(delay);
    }
  }
  
  const totalTime = Date.now() - startTime;
  return {
    success: false,
    error: lastError,
    attempts,
    totalTime,
  };
}

// Specialized retry for authentication-required API calls
export async function retryAuthenticatedApiCall<T>(
  apiCall: () => Promise<T>,
  refreshAuth: () => Promise<boolean>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  context?: string
): Promise<RetryResult<T>> {
  let authRefreshed = false;
  
  const enhancedApiCall = async (): Promise<T> => {
    try {
      return await apiCall();
    } catch (error) {
      const statusCode = (error as any)?.status || (error as any)?.statusCode;
      
      // If it's an auth error and we haven't tried refreshing yet
      if (isAuthError(error instanceof Error ? error : new Error(String(error)), statusCode) && !authRefreshed) {
        console.log(`🔄 Authentication error detected, attempting to refresh token${context ? ` (${context})` : ''}`);
        
        const refreshSuccess = await refreshAuth();
        if (refreshSuccess) {
          authRefreshed = true;
          console.log(`✅ Token refreshed successfully, retrying API call${context ? ` (${context})` : ''}`);
          return await apiCall(); // Retry with new token
        } else {
          console.log(`❌ Token refresh failed${context ? ` (${context})` : ''}`);
          throw new Error('Authentication failed - please login again');
        }
      }
      
      throw error;
    }
  };
  
  return retryApiCall(enhancedApiCall, config, context);
}

// Error categorization for user-friendly messages
export function categorizeError(error: Error, statusCode?: number): {
  category: 'network' | 'auth' | 'validation' | 'server' | 'unknown';
  userMessage: string;
  shouldRetry: boolean;
} {
  if (isAuthError(error, statusCode)) {
    return {
      category: 'auth',
      userMessage: 'Authentication required. Please login again.',
      shouldRetry: false,
    };
  }
  
  if (isRetryableError(error, statusCode)) {
    return {
      category: 'network',
      userMessage: 'Network error. Please check your connection and try again.',
      shouldRetry: true,
    };
  }
  
  if (statusCode === 400 || statusCode === 422) {
    return {
      category: 'validation',
      userMessage: 'Invalid data provided. Please check your information.',
      shouldRetry: false,
    };
  }
  
  if (statusCode && statusCode >= 500) {
    return {
      category: 'server',
      userMessage: 'Server error. Please try again later.',
      shouldRetry: true,
    };
  }
  
  return {
    category: 'unknown',
    userMessage: error.message || 'An unexpected error occurred.',
    shouldRetry: false,
  };
}
