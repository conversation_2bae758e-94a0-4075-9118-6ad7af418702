/**
 * Utility functions for KYC progress management and validation
 */

import { KYCApplication } from '@/redux/slices/kycSlice';
import {
  UnifiedKYCApplication,
  transformToUnifiedApplication,
  validateProgressConsistency,
  getNextIncompleteStep,
  getResumeRoute,
} from '@/types/unifiedDashboard';

/**
 * Calculate the actual progress percentage based on form completion
 */
export function calculateActualProgress(application: KYCApplication): number {
  const unifiedApp = transformToUnifiedApplication(application);
  return unifiedApp.overallProgress;
}

/**
 * Determine if an application is truly completed
 */
export function isApplicationTrulyCompleted(application: KYCApplication): boolean {
  const unifiedApp = transformToUnifiedApplication(application);
  return unifiedApp.isCompleted;
}

/**
 * Get the next step a user should complete
 */
export function getNextStepForApplication(application: KYCApplication): number {
  const unifiedApp = transformToUnifiedApplication(application);
  return getNextIncompleteStep(unifiedApp);
}

/**
 * Get the route for resuming an application
 */
export function getResumeRouteForApplication(application: KYCApplication): string {
  const nextStep = getNextStepForApplication(application);
  return getResumeRoute(nextStep);
}

/**
 * Validate application progress and return issues
 */
export function validateApplicationProgress(application: KYCApplication): {
  isValid: boolean;
  issues: string[];
  suggestedStep: number;
} {
  const unifiedApp = transformToUnifiedApplication(application);
  const validation = validateProgressConsistency(unifiedApp);
  
  return {
    isValid: validation.isConsistent,
    issues: validation.issues,
    suggestedStep: validation.suggestedStep,
  };
}

/**
 * Get a human-readable status for an application
 */
export function getApplicationStatus(application: KYCApplication): {
  status: string;
  progress: number;
  nextAction: string;
  canResume: boolean;
} {
  const unifiedApp = transformToUnifiedApplication(application);
  const nextStep = getNextIncompleteStep(unifiedApp);
  
  let nextAction = 'Start KYC';
  let canResume = false;
  
  if (unifiedApp.isCompleted) {
    nextAction = 'View Application';
  } else if (nextStep > 1) {
    nextAction = 'Continue KYC';
    canResume = true;
  }
  
  return {
    status: unifiedApp.isCompleted ? 'Completed' : 'In Progress',
    progress: unifiedApp.overallProgress,
    nextAction,
    canResume,
  };
}

/**
 * Debug function to log application progress details
 */
export function debugApplicationProgress(application: KYCApplication): void {
  const unifiedApp = transformToUnifiedApplication(application);
  const validation = validateProgressConsistency(unifiedApp);
  const nextStep = getNextIncompleteStep(unifiedApp);
  
  console.group(`KYC Application Debug: ${application.clientName}`);
  console.log('Application ID:', application.id);
  console.log('Current Step:', application.currentStep);
  console.log('Is Completed (stored):', application.isCompleted);
  console.log('Is Completed (calculated):', unifiedApp.isCompleted);
  console.log('Progress:', unifiedApp.overallProgress + '%');
  console.log('Next Incomplete Step:', nextStep);
  console.log('Resume Route:', getResumeRoute(nextStep));
  
  if (!validation.isConsistent) {
    console.warn('Progress Inconsistencies:', validation.issues);
    console.log('Suggested Step:', validation.suggestedStep);
  }
  
  console.log('Form Progress:', unifiedApp.formProgress);
  console.log('Verification Status:', unifiedApp.verificationStatus);
  console.groupEnd();
}

/**
 * Export all utility functions for easy import
 */
export const KYCProgressUtils = {
  calculateActualProgress,
  isApplicationTrulyCompleted,
  getNextStepForApplication,
  getResumeRouteForApplication,
  validateApplicationProgress,
  getApplicationStatus,
  debugApplicationProgress,
};
