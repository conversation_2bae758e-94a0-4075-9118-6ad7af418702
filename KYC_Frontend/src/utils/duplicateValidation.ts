import { kycService } from "@/services/kycService";

/**
 * Validation utility for checking duplicate email and phone numbers
 */

export interface DuplicateValidationResult {
  isValid: boolean;
  emailExists: boolean;
  phoneExists: boolean;
  errors: string[];
}

/**
 * Check if email and phone number are already registered
 * @param email - Email address to check
 * @param phone_no - Phone number to check
 * @returns Promise<DuplicateValidationResult>
 */
export const validateNoDuplicates = async (
  email: string,
  phone_no: string
): Promise<DuplicateValidationResult> => {
  try {
    const duplicateCheck = await kycService.checkDuplicateContact(email, phone_no);
    
    const errors: string[] = [];
    
    if (duplicateCheck.emailExists) {
      errors.push(`Email "${email}" is already registered`);
    }
    
    if (duplicateCheck.phoneExists) {
      errors.push(`Phone number "${phone_no}" is already registered`);
    }
    
    return {
      isValid: !duplicateCheck.hasConflict,
      emailExists: duplicateCheck.emailExists,
      phoneExists: duplicateCheck.phoneExists,
      errors
    };
  } catch (error) {
    console.error("Error validating duplicates:", error);
    // If validation fails, assume no duplicates to allow operation
    return {
      isValid: true,
      emailExists: false,
      phoneExists: false,
      errors: []
    };
  }
};

/**
 * Check if only email is already registered
 * @param email - Email address to check
 * @returns Promise<boolean>
 */
export const validateEmailNotDuplicate = async (email: string): Promise<boolean> => {
  try {
    return !(await kycService.checkEmailExists(email));
  } catch (error) {
    console.error("Error validating email:", error);
    return true; // Assume valid if check fails
  }
};

/**
 * Check if only phone number is already registered
 * @param phone_no - Phone number to check
 * @returns Promise<boolean>
 */
export const validatePhoneNotDuplicate = async (phone_no: string): Promise<boolean> => {
  try {
    return !(await kycService.checkPhoneExists(phone_no));
  } catch (error) {
    console.error("Error validating phone:", error);
    return true; // Assume valid if check fails
  }
};

/**
 * Debounced validation function for real-time form validation
 * @param email - Email address to check
 * @param phone_no - Phone number to check
 * @param delay - Delay in milliseconds (default: 500ms)
 * @returns Promise<DuplicateValidationResult>
 */
export const debouncedValidateNoDuplicates = (() => {
  let timeoutId: NodeJS.Timeout;
  
  return (
    email: string,
    phone_no: string,
    delay: number = 500
  ): Promise<DuplicateValidationResult> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        const result = await validateNoDuplicates(email, phone_no);
        resolve(result);
      }, delay);
    });
  };
})();

/**
 * Format duplicate validation errors for display
 * @param result - DuplicateValidationResult
 * @returns string - Formatted error message
 */
export const formatDuplicateErrors = (result: DuplicateValidationResult): string => {
  if (result.isValid) {
    return "";
  }
  
  return result.errors.join(". ") + ". Please use different credentials.";
};

/**
 * Get user-friendly error message for duplicate validation
 * @param emailExists - Whether email exists
 * @param phoneExists - Whether phone exists
 * @returns string - User-friendly error message
 */
export const getDuplicateErrorMessage = (emailExists: boolean, phoneExists: boolean): string => {
  if (emailExists && phoneExists) {
    return "Both email and phone number are already registered. Please use different credentials.";
  } else if (emailExists) {
    return "This email address is already registered. Please use a different email.";
  } else if (phoneExists) {
    return "This phone number is already registered. Please use a different phone number.";
  }
  return "";
};
