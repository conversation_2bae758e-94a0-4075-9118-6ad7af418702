import { toast } from "@/components/ui/sonner";

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Structured error interface
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context?: Record<string, any>;
  timestamp: Date;
  userMessage: string;
}

// Network error detection
const isNetworkError = (error: any): boolean => {
  return (
    !navigator.onLine ||
    error.name === 'NetworkError' ||
    error.message?.includes('fetch') ||
    error.message?.includes('network') ||
    error.code === 'NETWORK_ERROR'
  );
};

// Server error detection (5xx status codes)
const isServerError = (error: any): boolean => {
  return error.status >= 500 && error.status < 600;
};

// Client error detection (4xx status codes)
const isClientError = (error: any): boolean => {
  return error.status >= 400 && error.status < 500;
};

// Authentication error detection
const isAuthError = (error: any): boolean => {
  return error.status === 401 || error.message?.includes('unauthorized');
};

// Authorization error detection
const isAuthorizationError = (error: any): boolean => {
  return error.status === 403 || error.message?.includes('forbidden');
};

// Validation error detection
const isValidationError = (error: any): boolean => {
  return (
    error.status === 422 ||
    error.message?.includes('validation') ||
    error.message?.includes('invalid')
  );
};

// Error categorization function
export const categorizeError = (error: any): ErrorType => {
  if (isNetworkError(error)) return ErrorType.NETWORK;
  if (isAuthError(error)) return ErrorType.AUTHENTICATION;
  if (isAuthorizationError(error)) return ErrorType.AUTHORIZATION;
  if (isValidationError(error)) return ErrorType.VALIDATION;
  if (isServerError(error)) return ErrorType.SERVER;
  if (isClientError(error)) return ErrorType.CLIENT;
  return ErrorType.UNKNOWN;
};

// Error severity determination
export const determineSeverity = (errorType: ErrorType): ErrorSeverity => {
  switch (errorType) {
    case ErrorType.NETWORK:
      return ErrorSeverity.HIGH;
    case ErrorType.AUTHENTICATION:
      return ErrorSeverity.HIGH;
    case ErrorType.AUTHORIZATION:
      return ErrorSeverity.MEDIUM;
    case ErrorType.SERVER:
      return ErrorSeverity.HIGH;
    case ErrorType.VALIDATION:
      return ErrorSeverity.LOW;
    case ErrorType.CLIENT:
      return ErrorSeverity.MEDIUM;
    default:
      return ErrorSeverity.MEDIUM;
  }
};

// User-friendly error messages
export const getUserFriendlyMessage = (errorType: ErrorType, originalMessage?: string): string => {
  switch (errorType) {
    case ErrorType.NETWORK:
      return "Please check your internet connection and try again.";
    case ErrorType.AUTHENTICATION:
      return "Your session has expired. Please log in again.";
    case ErrorType.AUTHORIZATION:
      return "You don't have permission to perform this action.";
    case ErrorType.VALIDATION:
      return originalMessage || "Please check your input and try again.";
    case ErrorType.SERVER:
      return "Our servers are experiencing issues. Please try again later.";
    case ErrorType.CLIENT:
      return originalMessage || "There was an issue with your request. Please try again.";
    default:
      return "An unexpected error occurred. Please try again.";
  }
};

// Main error processing function
export const processError = (error: any, context?: Record<string, any>): AppError => {
  const errorType = categorizeError(error);
  const severity = determineSeverity(errorType);
  const userMessage = getUserFriendlyMessage(errorType, error.message);

  return {
    type: errorType,
    severity,
    message: error.message || 'Unknown error',
    originalError: error instanceof Error ? error : new Error(String(error)),
    context,
    timestamp: new Date(),
    userMessage,
  };
};

// Error logging function
export const logError = (appError: AppError): void => {
  const logData = {
    type: appError.type,
    severity: appError.severity,
    message: appError.message,
    context: appError.context,
    timestamp: appError.timestamp.toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('App Error:', logData);
    if (appError.originalError) {
      console.error('Original Error:', appError.originalError);
    }
  }

  // In production, you would send this to your error tracking service
  // Example: Sentry, LogRocket, etc.
  // errorTrackingService.captureError(logData);
};

// Error display function
export const displayError = (appError: AppError): void => {
  const { severity, userMessage } = appError;

  switch (severity) {
    case ErrorSeverity.LOW:
      toast.info(userMessage);
      break;
    case ErrorSeverity.MEDIUM:
      toast.error(userMessage);
      break;
    case ErrorSeverity.HIGH:
    case ErrorSeverity.CRITICAL:
      toast.error(userMessage, {
        duration: 10000, // Show longer for critical errors
      });
      break;
  }
};

// Main error handler function
export const handleError = (error: any, context?: Record<string, any>): AppError => {
  const appError = processError(error, context);
  logError(appError);
  displayError(appError);
  return appError;
};

// Async error wrapper for API calls
export const withErrorHandling = async <T>(
  asyncFn: () => Promise<T>,
  context?: Record<string, any>
): Promise<T | null> => {
  try {
    return await asyncFn();
  } catch (error) {
    handleError(error, context);
    return null;
  }
};

// React error boundary helper
export const createErrorBoundaryHandler = (componentName: string) => {
  return (error: Error, errorInfo: any) => {
    const appError = processError(error, {
      component: componentName,
      errorInfo,
    });
    logError(appError);
  };
};
