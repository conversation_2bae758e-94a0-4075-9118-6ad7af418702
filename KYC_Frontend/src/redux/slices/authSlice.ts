import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { User } from "@/types/user";

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  otpVerified: {
    email: boolean;
    mobile: boolean;
  };
  forgotPasswordEmail: string | null;
  registrationData: User | null;
  registrationStep:
    | "register"
    | "email-otp"
    | "mobile-otp"
    | "profile-picture"
    | "completed";
}

// Load auth state from localStorage if available
const loadAuthFromStorage = (): Partial<AuthState> => {
  try {
    const accessToken = localStorage.getItem("access_token");
    const refreshToken = localStorage.getItem("refresh_token");
    const userDataString = localStorage.getItem("user_data");
    const expiry = localStorage.getItem("token_expiry");

    if (accessToken && refreshToken && userDataString && expiry) {
      const expiryTime = parseInt(expiry);

      // Check if tokens are not expired
      if (Date.now() < expiryTime) {
        const userData = JSON.parse(userDataString);
        return {
          user: userData,
          isAuthenticated: true,
          accessToken,
          refreshToken,
        };
      } else {
        // Tokens expired, clear everything
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
        localStorage.removeItem("user_data");
        localStorage.removeItem("token_expiry");
      }
    }
  } catch (error) {
    console.error("Failed to load auth state from storage:", error);
    // Clear corrupted data
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    localStorage.removeItem("user_data");
    localStorage.removeItem("token_expiry");
  }
  return {};
};

const storedAuth = loadAuthFromStorage();

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  accessToken: null,
  refreshToken: null,
  otpVerified: {
    email: false,
    mobile: false,
  },
  forgotPasswordEmail: null,
  registrationData: null,
  registrationStep: "register",
  ...storedAuth,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (
      state,
      action: PayloadAction<{
        user: User;
        accessToken: string;
        refreshToken: string;
      }>
    ) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
      state.error = null;

      // Persist to localStorage with expiry
      const expiryTime = Date.now() + 24 * 60 * 60 * 1000; // 24 hours from now
      localStorage.setItem("access_token", action.payload.accessToken);
      localStorage.setItem("refresh_token", action.payload.refreshToken);
      localStorage.setItem("user_data", JSON.stringify(action.payload.user));
      localStorage.setItem("token_expiry", expiryTime.toString());
    },
    loginFailure: (state, action: PayloadAction<string | null>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.error = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.accessToken = null;
      state.refreshToken = null;
      state.error = null;

      // Clear localStorage
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("user_data");
      localStorage.removeItem("token_expiry");
    },
    setOtpVerified: (
      state,
      action: PayloadAction<{ type: "email" | "mobile"; value: boolean }>
    ) => {
      state.otpVerified[action.payload.type] = action.payload.value;
    },
    resetOtpVerification: (state) => {
      state.otpVerified = {
        email: false,
        mobile: false,
      };
    },
    setForgotPasswordEmail: (state, action: PayloadAction<string>) => {
      state.forgotPasswordEmail = action.payload;
    },
    setRegistrationData: (state, action: PayloadAction<User | null>) => {
      state.registrationData = action.payload;
    },
    clearRegistrationData: (state) => {
      state.registrationData = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        // Update localStorage with new user data
        localStorage.setItem("user_data", JSON.stringify(state.user));
      }
    },
    setRegistrationStep: (
      state,
      action: PayloadAction<
        | "register"
        | "email-otp"
        | "mobile-otp"
        | "profile-picture"
        | "completed"
      >
    ) => {
      state.registrationStep = action.payload;
    },
    resetRegistrationFlow: (state) => {
      state.registrationData = null;
      state.registrationStep = "register";
      state.otpVerified = {
        email: false,
        mobile: false,
      };
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  setOtpVerified,
  resetOtpVerification,
  setForgotPasswordEmail,
  setRegistrationData,
  clearRegistrationData,
  updateUser,
  setRegistrationStep,
  resetRegistrationFlow,
} = authSlice.actions;

export const selectUser = (state: RootState) => state.auth.user;
export const selectIsAuthenticated = (state: RootState) =>
  state.auth.isAuthenticated;
export const selectAuthLoading = (state: RootState) => state.auth.isLoading;
export const selectAuthError = (state: RootState) => state.auth.error;
export const selectOtpVerified = (state: RootState) => state.auth.otpVerified;
export const selectForgotPasswordEmail = (state: RootState) =>
  state.auth.forgotPasswordEmail;
export const selectRegistrationData = (state: RootState) =>
  state.auth.registrationData;
export const selectRegistrationStep = (state: RootState) =>
  state.auth.registrationStep;

export default authSlice.reducer;
