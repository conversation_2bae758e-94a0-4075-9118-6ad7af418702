// Unified Dashboard Data Types for Consolidated KYC Display

export interface KYCFormProgress {
  personalInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  additionalInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  addressInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  documentInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    documentsUploaded: number;
    totalDocumentsRequired: number;
    progress: number; // 0-100
  };
  facialVerification: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
}

export interface VerificationStatus {
  email: {
    verified: boolean;
    verifiedAt?: string;
    email: string;
  };
  mobile: {
    verified: boolean;
    verifiedAt?: string;
    phone: string;
  };
  facial: {
    verified: boolean;
    verifiedAt?: string;
  };
  documents: {
    verified: boolean;
    verifiedAt?: string;
    documentsVerified: number;
    totalDocuments: number;
  };
}

export interface UnifiedKYCApplication {
  // Basic Application Info
  id: string;
  clientName: string;
  email: string;
  phone: string;

  // Overall Status
  status:
    | "draft"
    | "in_progress"
    | "submitted"
    | "under_review"
    | "approved"
    | "rejected";
  isCompleted: boolean;
  overallProgress: number; // 0-100 percentage

  // Timestamps
  createdAt: string;
  updatedAt: string;
  submissionDate?: string;
  completionDate?: string;

  // Detailed Progress Tracking
  formProgress: KYCFormProgress;
  verificationStatus: VerificationStatus;

  // Current Step Information
  currentStep: number;
  currentStepName: string;
  nextStepName?: string;

  // API Integration Data
  apiSubmissionIds: {
    personalInfo?: string;
    addressInfo?: string;
    documentIds?: Record<string, string>; // document type -> API ID
  };

  // Error Tracking
  hasErrors: boolean;
  lastError?: string;
  lastErrorAt?: string;

  // Sync Status
  lastSyncAt?: string;
  syncStatus: "synced" | "pending" | "failed";
}

export interface DashboardTableColumn {
  key: string;
  label: string;
  sortable: boolean;
  width?: string;
  align?: "left" | "center" | "right";
  render?: (application: UnifiedKYCApplication) => React.ReactNode;
}

export interface DashboardTableConfig {
  columns: DashboardTableColumn[];
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  showActions: boolean;
  showProgress: boolean;
  showDetailedStatus: boolean;
}

// Default table configuration
export const DEFAULT_DASHBOARD_CONFIG: DashboardTableConfig = {
  columns: [
    {
      key: "clientInfo",
      label: "Client Information",
      sortable: true,
      width: "200px",
      align: "left",
    },
    {
      key: "contactVerification",
      label: "Contact Verification",
      sortable: false,
      width: "180px",
      align: "center",
    },
    {
      key: "kycProgress",
      label: "KYC Progress",
      sortable: true,
      width: "150px",
      align: "center",
    },
    {
      key: "overallStatus",
      label: "Status",
      sortable: true,
      width: "120px",
      align: "center",
    },
    {
      key: "lastUpdated",
      label: "Last Updated",
      sortable: true,
      width: "130px",
      align: "center",
    },
    {
      key: "actions",
      label: "Actions",
      sortable: false,
      width: "150px",
      align: "center",
    },
  ],
  sortBy: "updatedAt",
  sortDirection: "desc",
  showActions: true,
  showProgress: true,
  showDetailedStatus: true,
};

// Utility functions for data transformation
export function calculateOverallProgress(
  formProgress: KYCFormProgress
): number {
  const weights = {
    personalInfo: 20,
    additionalInfo: 15,
    addressInfo: 20,
    documentInfo: 25,
    facialVerification: 20,
  };

  const totalWeight = Object.values(weights).reduce(
    (sum, weight) => sum + weight,
    0
  );

  const weightedProgress =
    formProgress.personalInfo.progress * weights.personalInfo +
    formProgress.additionalInfo.progress * weights.additionalInfo +
    formProgress.addressInfo.progress * weights.addressInfo +
    formProgress.documentInfo.progress * weights.documentInfo +
    formProgress.facialVerification.progress * weights.facialVerification;

  return Math.round(weightedProgress / totalWeight);
}

export function getStatusColor(
  status: UnifiedKYCApplication["status"]
): string {
  switch (status) {
    case "draft":
      return "bg-gray-100 text-gray-800 border-gray-300";
    case "in_progress":
      return "bg-blue-100 text-blue-800 border-blue-300";
    case "submitted":
      return "bg-yellow-100 text-yellow-800 border-yellow-300";
    case "under_review":
      return "bg-orange-100 text-orange-800 border-orange-300";
    case "approved":
      return "bg-green-100 text-green-800 border-green-300";
    case "rejected":
      return "bg-red-100 text-red-800 border-red-300";
    default:
      return "bg-gray-100 text-gray-800 border-gray-300";
  }
}

export function getStatusText(
  status: UnifiedKYCApplication["status"],
  isCompleted: boolean
): string {
  if (isCompleted) {
    return status === "approved" ? "Approved" : "Completed";
  }

  switch (status) {
    case "draft":
      return "Draft";
    case "in_progress":
      return "In Progress";
    case "submitted":
      return "Submitted";
    case "under_review":
      return "Under Review";
    case "approved":
      return "Approved";
    case "rejected":
      return "Rejected";
    default:
      return "Unknown";
  }
}

export function getNextActionText(application: UnifiedKYCApplication): string {
  if (application.isCompleted) {
    return "View";
  }

  if (application.hasErrors) {
    return "Fix Issues";
  }

  switch (application.currentStep) {
    case 1:
      return "Start Verification";
    case 2:
      return "Complete Personal Info";
    case 3:
      return "Complete Additional Info";
    case 4:
      return "Complete Address";
    case 5:
      return "Complete Facial Verification";
    case 6:
      return "Upload Documents";
    case 7:
      return "Review & Submit";
    default:
      return "Continue";
  }
}

// Helper function to determine actual completion status based on form data
function isFormSectionCompleted(
  application: any,
  section: "personal" | "additional" | "address" | "facial" | "documents"
): boolean {
  switch (section) {
    case "personal":
      return !!(
        application.personalInfo?.firstName &&
        application.personalInfo?.lastName &&
        application.personalInfo?.email &&
        application.personalInfo?.mobile &&
        application.personalInfo?.dateOfBirth &&
        application.personalInfo?.gender &&
        application.personalInfo?.nationality
      );
    case "additional":
      return !!(
        application.additionalInfo?.occupation &&
        application.additionalInfo?.sourceOfIncome &&
        application.additionalInfo?.annualIncome &&
        application.additionalInfo?.purposeOfAccount
      );
    case "address":
      return !!(
        application.addressDetails?.street &&
        application.addressDetails?.city &&
        application.addressDetails?.state &&
        application.addressDetails?.postalCode &&
        application.addressDetails?.country
      );
    case "facial":
      return application.verificationStatus?.facial === true;
    case "documents": {
      // Check if all required documents are uploaded and verified
      const requiredDocs = ["passport", "nationalId"]; // Adjust based on requirements
      return requiredDocs.every(
        (docType) =>
          application.verificationStatus?.documents?.[docType]?.verified ===
          true
      );
    }
    default:
      return false;
  }
}

// Enhanced progress calculation based on actual form completion
function calculateFormProgress(application: any): KYCFormProgress {
  const personalCompleted = isFormSectionCompleted(application, "personal");
  const additionalCompleted = isFormSectionCompleted(application, "additional");
  const addressCompleted = isFormSectionCompleted(application, "address");
  const facialCompleted = isFormSectionCompleted(application, "facial");
  const documentsCompleted = isFormSectionCompleted(application, "documents");

  return {
    personalInfo: {
      completed: personalCompleted,
      completedAt: personalCompleted ? application.updatedAt : undefined,
      progress: personalCompleted ? 100 : application.currentStep >= 2 ? 50 : 0,
    },
    additionalInfo: {
      completed: additionalCompleted,
      completedAt: additionalCompleted ? application.updatedAt : undefined,
      progress: additionalCompleted
        ? 100
        : application.currentStep >= 3
        ? 50
        : 0,
    },
    addressInfo: {
      completed: addressCompleted,
      completedAt: addressCompleted ? application.updatedAt : undefined,
      progress: addressCompleted ? 100 : application.currentStep >= 4 ? 50 : 0,
    },
    documentInfo: {
      completed: documentsCompleted,
      completedAt: documentsCompleted ? application.updatedAt : undefined,
      documentsUploaded: Object.keys(
        application.verificationStatus?.documents || {}
      ).length,
      totalDocumentsRequired: 2,
      progress: documentsCompleted
        ? 100
        : application.currentStep >= 6
        ? 50
        : 0,
    },
    facialVerification: {
      completed: facialCompleted,
      completedAt: facialCompleted ? application.updatedAt : undefined,
      progress: facialCompleted ? 100 : application.currentStep >= 5 ? 50 : 0,
    },
  };
}

// Determine if KYC is truly completed
function isKYCTrulyCompleted(
  application: any,
  formProgress: KYCFormProgress
): boolean {
  // KYC is only completed when all sections are done AND currentStep >= 10 (completion step)
  const allSectionsCompleted =
    formProgress.personalInfo.completed &&
    formProgress.additionalInfo.completed &&
    formProgress.addressInfo.completed &&
    formProgress.facialVerification.completed &&
    formProgress.documentInfo.completed;

  return allSectionsCompleted && application.currentStep >= 10;
}

// Transform existing KYC application to unified format
export function transformToUnifiedApplication(
  application: any, // Existing KYC application type
  apiData?: {
    personalInfo?: any[];
    addressInfo?: any[];
    documentInfo?: any[];
  }
): UnifiedKYCApplication {
  // Calculate form progress based on actual completion status
  const formProgress = calculateFormProgress(application);
  const overallProgress = calculateOverallProgress(formProgress);
  const trulyCompleted = isKYCTrulyCompleted(application, formProgress);

  // Determine proper status based on actual completion and current step
  let status: UnifiedKYCApplication["status"] = "draft";
  if (trulyCompleted) {
    status = application.status === "approved" ? "approved" : "submitted";
  } else if (application.currentStep > 1) {
    status = "in_progress";
  }

  return {
    id: application.id,
    clientName: application.clientName || "Unknown Client",
    email: application.email || "",
    phone: application.phone || "",

    status,
    isCompleted: trulyCompleted, // Use the calculated completion status
    overallProgress,

    createdAt: application.createdAt || new Date().toISOString(),
    updatedAt: application.updatedAt || new Date().toISOString(),
    submissionDate: application.submissionDate,
    completionDate: application.completionDate,

    formProgress,
    verificationStatus: {
      email: {
        verified: application.verificationStatus?.email || false,
        verifiedAt: application.verificationStatus?.email
          ? application.updatedAt
          : undefined,
        email: application.email || "",
      },
      mobile: {
        verified: application.verificationStatus?.phone_no || false,
        verifiedAt: application.verificationStatus?.phone_no
          ? application.updatedAt
          : undefined,
        phone: application.phone || "",
      },
      facial: {
        verified: application.verificationStatus?.facial || false,
        verifiedAt: application.verificationStatus?.facial
          ? application.updatedAt
          : undefined,
      },
      documents: {
        verified: application.verificationStatus?.documents || false,
        verifiedAt: application.verificationStatus?.documents
          ? application.updatedAt
          : undefined,
        documentsVerified:
          application.documents?.filter((doc: any) => doc.verified).length || 0,
        totalDocuments: application.documents?.length || 0,
      },
    },

    currentStep: application.currentStep || 1,
    currentStepName: getStepName(application.currentStep || 1),
    nextStepName: getStepName((application.currentStep || 1) + 1),

    apiSubmissionIds: {
      personalInfo: application.apiSubmissionId?.personalId,
      addressInfo: application.apiSubmissionId?.addressId,
      documentIds: application.apiSubmissionId?.documentIds || {},
    },

    hasErrors: false, // This can be enhanced based on API sync status
    lastError: undefined,
    lastErrorAt: undefined,

    lastSyncAt: new Date().toISOString(),
    syncStatus: "synced",
  };
}

function getStepName(step: number): string {
  switch (step) {
    case 1:
      return "Contact Verification";
    case 2:
      return "Personal Information";
    case 3:
      return "Additional Information";
    case 4:
      return "Address Details";
    case 5:
      return "Facial Verification";
    case 6:
      return "Document Upload";
    case 7:
      return "Review & Submit";
    case 8:
      return "Summary";
    case 9:
      return "Review & Confirm";
    case 10:
      return "Completed";
    default:
      return "Unknown Step";
  }
}

// Resume functionality utilities
export function getNextIncompleteStep(
  application: UnifiedKYCApplication
): number {
  const { formProgress, verificationStatus } = application;

  // Check contact verification first
  if (
    !verificationStatus.email.verified ||
    !verificationStatus.mobile.verified
  ) {
    return 1;
  }

  // Check personal information
  if (!formProgress.personalInfo.completed) {
    return 2;
  }

  // Check additional information
  if (!formProgress.additionalInfo.completed) {
    return 3;
  }

  // Check address information
  if (!formProgress.addressInfo.completed) {
    return 4;
  }

  // Check facial verification
  if (!formProgress.facialVerification.completed) {
    return 5;
  }

  // Check document upload
  if (!formProgress.documentInfo.completed) {
    return 6;
  }

  // If all sections are complete, go to review
  if (application.currentStep < 9) {
    return 9; // Review & Confirm
  }

  // If everything is done, go to completion
  return 10;
}

export function getResumeRoute(step: number): string {
  switch (step) {
    case 1:
      return "/kyc/verification-process";
    case 2:
      return "/kyc/personal";
    case 3:
      return "/kyc/additional";
    case 4:
      return "/kyc/address";
    case 5:
      return "/kyc/facial";
    case 6:
      return "/kyc/documents";
    case 7:
      return "/kyc/verification";
    case 8:
      return "/kyc/summary";
    case 9:
      return "/kyc/review";
    case 10:
      return "/kyc/completion";
    default:
      return "/kyc/verification-process";
  }
}

// Progress validation and recovery utilities
export function validateProgressConsistency(
  application: UnifiedKYCApplication
): {
  isConsistent: boolean;
  issues: string[];
  suggestedStep: number;
} {
  const issues: string[] = [];
  let suggestedStep = application.currentStep;

  // Check if progress matches completion status
  if (application.isCompleted && application.overallProgress < 100) {
    issues.push(
      "Application marked as completed but progress is less than 100%"
    );
    suggestedStep = getNextIncompleteStep(application);
  }

  if (!application.isCompleted && application.overallProgress === 100) {
    issues.push("Progress is 100% but application not marked as completed");
  }

  // Check if current step is ahead of actual progress
  const nextIncompleteStep = getNextIncompleteStep(application);
  if (application.currentStep > nextIncompleteStep + 1) {
    issues.push(
      `Current step (${application.currentStep}) is ahead of actual progress`
    );
    suggestedStep = nextIncompleteStep;
  }

  return {
    isConsistent: issues.length === 0,
    issues,
    suggestedStep,
  };
}
