import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "./redux/store";
import { useAppSelector } from "./redux/hooks";
import { selectIsAuthenticated } from "./redux/slices/authSlice";
import ProtectedRoute from "@/components/ProtectedRoute";
import ErrorBoundary from "@/components/ErrorBoundary";
import { useAuthInit } from "@/hooks/useAuthInit";

// Pages
import Login from "./pages/Login";
import LoginOTP from "./pages/LoginOTP";
import Register from "./pages/Register";
import VerifyOtpEmail from "./pages/VerifyOtpEmail";
import VerifyOtpMobile from "./pages/VerifyOtpMobile";
// import UploadProfile from "./pages/UpdateProfile";
import AccountCreated from "./pages/AccountCreated";
import Dashboard from "./pages/Dashboard";
import Support from "./pages/Support";
import ForgotPassword from "./pages/ForgotPassword";
import VerifyResetOtp from "./pages/VerifyResetOtp";
import ResetPassword from "./pages/ResetPassword";
import PasswordChanged from "./pages/PasswordChanged";
import NotFound from "./pages/NotFound";
import Index from "./pages/Index";
import Profile from "./pages/Profile";
// import UpdateProfile from "./pages/UpdateProfile";
import ChangePassword from "./pages/ChangePassword";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";

// KYC pages
import PersonalInfo from "./pages/kyc/PersonalInfo";
import AddressDetails from "./pages/kyc/AddressDetails";
import AdditionalInfo from "./pages/kyc/AdditionalInfo";
import VerifyEmail from "./pages/kyc/VerifyEmail";
import VerifyMobile from "./pages/kyc/VerifyMobile";
import FacialVerification from "./pages/kyc/FacialVerification";
import DocumentUpload from "./pages/kyc/DocumentUpload";
import Verification from "./pages/kyc/Verification";
import Summary from "./pages/kyc/Summary";
import ReVerification from "./pages/kyc/ReVerification";
import Review from "./pages/kyc/Review";
import Completion from "./pages/kyc/Completion";
import VerificationProcess from "./pages/kyc/VerificationProcess";
import ContactVerification from "./pages/kyc/ContactVerification";

const queryClient = new QueryClient();

// Auth route component (redirect to dashboard if already logged in)
const AuthRoute = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const AppRoutes = () => {
  // Initialize authentication state from stored tokens
  const { isInitialized } = useAuthInit();

  // Show loading screen while initializing authentication
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#AF47D2] mx-auto mb-4"></div>
          <h2 className="text-xl font-medium text-gray-600">Loading...</h2>
          <p className="text-gray-500 mt-2">
            Please wait while we initialize your session
          </p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Root route */}
      <Route path="/" element={<Index />} />

      {/* Auth routes */}
      <Route
        path="/login"
        element={
          <AuthRoute>
            <Login />
          </AuthRoute>
        }
      />
      <Route
        path="/login-otp"
        element={
          <AuthRoute>
            <LoginOTP />
          </AuthRoute>
        }
      />
      <Route
        path="/register"
        element={
          <AuthRoute>
            <Register />
          </AuthRoute>
        }
      />
      <Route
        path="/verify-otp-email"
        element={
          <AuthRoute>
            <VerifyOtpEmail />
          </AuthRoute>
        }
      />
      <Route
        path="/verify-otp-mobile"
        element={
          <AuthRoute>
            <VerifyOtpMobile />
          </AuthRoute>
        }
      />

      <Route
        path="/account-created"
        element={
          <AuthRoute>
            <AccountCreated />
          </AuthRoute>
        }
      />
      <Route
        path="/forgot-password"
        element={
          <AuthRoute>
            <ForgotPassword />
          </AuthRoute>
        }
      />
      <Route
        path="/verify-reset-otp"
        element={
          <AuthRoute>
            <VerifyResetOtp />
          </AuthRoute>
        }
      />
      <Route
        path="/reset-password"
        element={
          <AuthRoute>
            <ResetPassword />
          </AuthRoute>
        }
      />
      <Route path="/password-changed" element={<PasswordChanged />} />

      {/* Accessible to both authenticated and unauthenticated users */}
      {/* <Route path="/upload-profile" element={<UploadProfile />} /> */}

      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/support"
        element={
          <ProtectedRoute>
            <Support />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        }
      />
      {/* <Route
        path="/update-profile"
        element={
          <ProtectedRoute>
            <UpdateProfile />
          </ProtectedRoute>
        }
      /> */}
      <Route
        path="/change-password"
        element={
          <ProtectedRoute>
            <ChangePassword />
          </ProtectedRoute>
        }
      />

      {/* KYC Routes */}
      <Route
        path="/kyc/verification-process"
        element={
          <ProtectedRoute>
            <VerificationProcess />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/contact-verification"
        element={
          <ProtectedRoute>
            <ContactVerification />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/verify-email"
        element={
          <ProtectedRoute>
            <VerifyEmail />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/verify-mobile"
        element={
          <ProtectedRoute>
            <VerifyMobile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/personal"
        element={
          <ProtectedRoute>
            <PersonalInfo />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/address"
        element={
          <ProtectedRoute>
            <AddressDetails />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/additional"
        element={
          <ProtectedRoute>
            <AdditionalInfo />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/facial"
        element={
          <ProtectedRoute>
            <FacialVerification />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/documents"
        element={
          <ProtectedRoute>
            <DocumentUpload />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/verification"
        element={
          <ProtectedRoute>
            <Verification />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/summary"
        element={
          <ProtectedRoute>
            <Summary />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/re-verification"
        element={
          <ProtectedRoute>
            <ReVerification />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/review"
        element={
          <ProtectedRoute>
            <Review />
          </ProtectedRoute>
        }
      />
      <Route
        path="/kyc/completion"
        element={
          <ProtectedRoute>
            <Completion />
          </ProtectedRoute>
        }
      />

      {/* Public routes */}
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />

      {/* Redirect root to dashboard if authenticated, otherwise to login */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />

      {/* 404 route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <ErrorBoundary>
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AppRoutes />
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </Provider>
  </ErrorBoundary>
);

export default App;
