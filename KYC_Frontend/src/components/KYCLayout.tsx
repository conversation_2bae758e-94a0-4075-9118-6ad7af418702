import React, { ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { X } from "lucide-react";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/slices/authSlice";
import DashboardHeader from "./dashboard/DashboardHeader";
import DashboardSidebar from "./dashboard/DashboardSidebar";

interface KYCLayoutProps {
  children: ReactNode;
  title: string;
  subtitle: string;
  showCloseButton?: boolean;
}

const KYCLayout: React.FC<KYCLayoutProps> = ({
  children,
  title,
  subtitle,
  showCloseButton = true,
}) => {
  const navigate = useNavigate();
  const user = useAppSelector(selectUser);
  // console.log(user)

  const handleClose = () => {
    navigate("/dashboard");
  };

  return (
    <div className="flex font-urbanist">
      {/* Left sidebar */}
      <DashboardSidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        <DashboardHeader />

        <main className="flex-1 p-6 flex justify-center">
          <div className="w-full max-w-4xl rounded-xl shadow-md overflow-hidden">
            <div className="relative p-8">
              {/* Close button */}
              {showCloseButton && (
                <button
                  className="absolute top-6 right-6 text-[#26355E] hover:text-gray-600"
                  onClick={handleClose}
                >
                  <X size={24} />
                </button>
              )}

              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800">{title}</h2>
                <p className="text-gray-600">{subtitle}</p>
              </div>

              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default KYCLayout;
