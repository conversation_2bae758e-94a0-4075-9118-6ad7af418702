import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/slices/authSlice";
import { Button } from "@/components/ui/button";
import { Camera } from "lucide-react";

const ProfilePicturePrompt = () => {
  const navigate = useNavigate();
  const user = useAppSelector(selectUser);

  const handleUploadProfilePicture = () => {
    navigate("/profile");
  };

  if (!user?.hasSkippedProfilePicture) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-purple/20">
      <div className="flex items-center">
        <div className="mr-4">
          <div className="w-12 h-12 rounded-full bg-purple/10 flex items-center justify-center">
            <Camera size={24} className="text-purple" />
          </div>
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-lg">Complete your profile</h3>
          <p className="text-gray-600">
            Add a profile picture to personalize your account
          </p>
        </div>
        <Button
          onClick={handleUploadProfilePicture}
          className="bg-purple text-white hover:bg-purple/90"
        >
          Upload Now
        </Button>
      </div>
    </div>
  );
};

export default ProfilePicturePrompt;
