import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/slices/authSlice";
import { LayoutDashboard, Menu } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

const DashboardSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const user = useAppSelector(selectUser);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleNavigate = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const isActive = (path: string) => location.pathname === path;

  const SidebarContent = () => (
    <div className="min-h-screen w-64 bg-white border-r border-gray-200 flex flex-col font-urbanist">
      <div className="p-6">
        <img src="/Images/logo 1.png" alt="Boticx Labs Logo" className="h-13" />
      </div>

      <div className="flex-grow mt-4">
        <div className="px-4 py-2">
          <div
            className={`${
              isActive("/dashboard")
                ? "bg-[#AF47D21A] bg-opacity-20 text-[#AF47D2]"
                : ""
            } rounded-lg p-3 flex items-center space-x-2 cursor-pointer`}
            onClick={() => handleNavigate("/dashboard")}
          >
            <LayoutDashboard size={18} className="text-[#AF47D2]" />
            <span>Dashboard</span>
          </div>
        </div>

        <div className="mt-4 px-4 py-2 border-t border-gray-200">
          <div
            className={`${
              isActive("/support")
                ? "bg-[#AF47D21A] bg-opacity-20 text-[#AF47D2]"
                : ""
            } rounded-lg p-3 flex items-center space-x-2 cursor-pointer mt-6`}
            onClick={() => handleNavigate("/support")}
          >
            <svg
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M12 8v4" />
              <path d="M12 16h.01" />
            </svg>
            <span>Support</span>
          </div>
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
        <div
          className="flex items-center p-2 cursor-pointer"
          onClick={() => handleNavigate("/profile")}
        >
          <Avatar
            className="w-10 h-10 mr-3"
            key={user?.profile_pic || user?.profilePicture || "no-pic"}
          >
            {user?.profile_pic || user?.profilePicture ? (
              <AvatarImage
                src={user.profile_pic || user.profilePicture}
                alt="Profile"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                }}
              />
            ) : (
              <AvatarFallback className="bg-[#AF47D2] text-white">
                {user?.name?.[0] || user?.firstName?.[0] || "U"}
              </AvatarFallback>
            )}
          </Avatar>
          <div className="flex-grow">
            <div className="font-medium text-sm">Welcome</div>
            <div className="text-sm font-bold">
              {user?.name || user?.firstName || "User"}
            </div>
          </div>
          <div className="ml-2">
            <button className="p-1">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="md:hidden fixed top-0 right-4 z-50">
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="bg-white">
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-64">
            <SidebarContent />
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <SidebarContent />
      </div>
    </>
  );
};

export default DashboardSidebar;
