import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  selectKYCApplications,
  selectCurrentApplicationId,
  switchToApplication,
  deleteApplication,
  selectVerifiedApplications,
  validateAndFixProgress,
} from "@/redux/slices/kycSlice";
import {
  getNextIncompleteStep,
  getResumeRoute,
  validateProgressConsistency,
  transformToUnifiedApplication,
} from "@/types/unifiedDashboard";
import { useKycApi } from "@/hooks/useKycApi";
import { useProgressValidation } from "@/hooks/useProgressValidation";
import { Button } from "@/components/ui/button";
import { CircleFadingPlus, Trash2, CircleCheck, RefreshCw } from "lucide-react";
import { toast } from "@/components/ui/sonner";

const MultiClientKycDashboard = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const allApplications = useAppSelector(selectKYCApplications);
  const applications = useAppSelector(selectVerifiedApplications); // Only show verified applications
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);

  // API integration
  const { isLoading, hasErrors, apiErrors, refreshData, lastSyncTimestamp } =
    useKycApi();

  // Automatically validate and fix progress inconsistencies
  useProgressValidation();

  // Handle Complete Your KYC button click - directly navigate to verification process
  const handleCompleteKYC = () => {
    // Simply navigate to verification process - application will be created after successful verification
    navigate("/kyc/verification-process");
  };

  const handleViewApplication = (applicationId: string) => {
    dispatch(switchToApplication(applicationId));
    navigate("/kyc/review");
  };

  const handleContinueApplication = (applicationId: string) => {
    dispatch(switchToApplication(applicationId));
    const application = allApplications?.find(
      (app) => app.id === applicationId
    );

    if (application) {
      // Transform to unified application for validation
      const unifiedApp = transformToUnifiedApplication(application);

      // Validate progress consistency and fix if needed
      const validation = validateProgressConsistency(unifiedApp);
      if (!validation.isConsistent) {
        console.warn("Progress inconsistency detected:", validation.issues);
        dispatch(validateAndFixProgress());
        toast.warning("Progress data was inconsistent and has been corrected.");
      }

      // Determine the next incomplete step for resume functionality
      const nextStep = getNextIncompleteStep(unifiedApp);
      const resumeRoute = getResumeRoute(nextStep);

      // Show a toast indicating where the user will resume
      if (nextStep !== application.currentStep) {
        toast.info(`Resuming from: ${getStepName(nextStep)}`);
      }

      navigate(resumeRoute);
    }
  };

  // Helper function to get step name for toast messages
  const getStepName = (step: number): string => {
    switch (step) {
      case 1:
        return "Contact Verification";
      case 2:
        return "Personal Information";
      case 3:
        return "Additional Information";
      case 4:
        return "Address Details";
      case 5:
        return "Facial Verification";
      case 6:
        return "Document Upload";
      case 7:
        return "Verification";
      case 8:
        return "Summary";
      case 9:
        return "Review & Confirm";
      case 10:
        return "Completion";
      default:
        return "Unknown Step";
    }
  };

  const handleDeleteApplication = (applicationId: string) => {
    const application = allApplications?.find(
      (app) => app.id === applicationId
    );
    if (application) {
      dispatch(deleteApplication(applicationId));
      toast.success(`Deleted KYC application for ${application.clientName}`);
    }
  };

  const getStatusText = (status: string, isCompleted: boolean) => {
    if (isCompleted) return "Completed";

    switch (status) {
      case "submitted":
        return "Under Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "in_progress":
        return "In Progress";
      default:
        return "Draft";
    }
  };

  return (
    <div className="w-full">
      {/* API Status and Refresh Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-bold">KYC Applications</h2>
          {lastSyncTimestamp && (
            <span className="text-sm text-gray-500">
              Last synced: {new Date(lastSyncTimestamp).toLocaleTimeString()}
            </span>
          )}
        </div>
        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          {hasErrors && (
            <span className="text-sm text-red-600">
              API sync issues detected
            </span>
          )}
        </div>
      </div>

      {/* API Error Display */}
      {hasErrors && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            API Sync Errors:
          </h3>
          <ul className="text-sm text-red-600 space-y-1">
            {apiErrors.personalInfo && (
              <li>• Personal Info: {apiErrors.personalInfo}</li>
            )}
            {apiErrors.addressInfo && (
              <li>• Address Info: {apiErrors.addressInfo}</li>
            )}
            {apiErrors.documentInfo && (
              <li>• Document Info: {apiErrors.documentInfo}</li>
            )}
            {apiErrors.fetchingApplications && (
              <li>• Applications: {apiErrors.fetchingApplications}</li>
            )}
          </ul>
        </div>
      )}

      {(applications?.length || 0) === 0 ? (
        <div className="text-center py-12">
          <div className="flex flex-col items-center">
            <div
              className="w-10 h-10 rounded-xl cursor-pointer bg-[#26355E] hover:bg-primary-navy/90 flex items-center justify-center text-white mb-4"
              onClick={handleCompleteKYC}
            >
              <CircleFadingPlus size={28} />
            </div>
            <Button
              className="bg-[#26355E] hover:bg-primary-navy/90 text-white py-6 px-12 md:px-44 rounded-lg text-lg font-medium"
              onClick={handleCompleteKYC}
            >
              Complete Your KYC
            </Button>
          </div>
        </div>
      ) : (
        <div className="overflow-x-auto rounded-lg border border-gray-200">
          <div className="min-w-[800px]">
            <table className="w-full">
              <thead className="bg-gray-50 rounded-md">
                <tr>
                  <th className="text-center py-4 px-2 sm:px-4">Person name</th>
                  <th className="text-center py-4 px-2 sm:px-4">
                    Facial verification
                  </th>
                  <th className="text-center py-4 px-2 sm:px-4">
                    Mobile verification
                  </th>
                  <th className="text-center py-4 px-2 sm:px-4">
                    Email verification
                  </th>
                  <th className="text-center py-4 px-2 sm:px-4">KYC</th>
                  <th className="text-center py-4 px-2 sm:px-4">
                    Submission Date
                  </th>
                  <th className="text-center py-4 px-2 sm:px-4">Preview</th>
                </tr>
              </thead>
              <tbody>
                {applications?.map((application) => (
                  <tr
                    key={application.id}
                    className={`border-b hover:bg-gray-50 ${
                      currentApplicationId === application.id
                        ? "bg-blue-50"
                        : ""
                    }`}
                  >
                    <td className="py-3 text-center px-2 sm:px-4">
                      <div className="flex flex-col">
                        <span className="font-medium text-sm">
                          {application.clientName}
                        </span>
                        {currentApplicationId === application.id && (
                          <span className="mt-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded w-fit">
                            Active
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-2 sm:px-4">
                      <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                        <span>
                          {application.verificationStatus.facial
                            ? "Validated"
                            : "Pending"}
                        </span>
                        <CircleCheck
                          size={16}
                          className={`ml-1 ${
                            application.verificationStatus.facial
                              ? "text-green-500"
                              : "text-gray-400"
                          }`}
                        />
                      </div>
                    </td>
                    <td className="py-3 px-2 sm:px-4">
                      <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                        <span>
                          {application.verificationStatus.mobile
                            ? "Validated"
                            : "Pending"}
                        </span>
                        <span className="text-gray-500 text-xs mt-1">
                          {application.phone}
                        </span>
                        <CircleCheck
                          size={16}
                          className={`ml-1 ${
                            application.verificationStatus.mobile
                              ? "text-green-500"
                              : "text-gray-400"
                          }`}
                        />
                      </div>
                    </td>
                    <td className="py-3 px-2 sm:px-4">
                      <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                        <span>
                          {application.verificationStatus.email
                            ? "Validated"
                            : "Pending"}
                        </span>
                        <span className="text-gray-500 text-xs mt-1">
                          {application.email}
                        </span>
                        <CircleCheck
                          size={16}
                          className={`ml-1 ${
                            application.verificationStatus.email
                              ? "text-green-500"
                              : "text-gray-400"
                          }`}
                        />
                      </div>
                    </td>
                    <td className="py-3 text-center px-2 sm:px-4">
                      <span className="px-2 sm:px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
                        {getStatusText(
                          application.status,
                          application.isCompleted
                        )}
                      </span>
                    </td>
                    <td className="py-3 text-center px-2 sm:px-4">
                      {application.submissionDate
                        ? new Date(
                            application.submissionDate
                          ).toLocaleDateString()
                        : "-"}
                    </td>
                    <td className="py-3 text-center px-2 sm:px-4">
                      <div className="flex items-center justify-center space-x-2">
                        {!application.isCompleted && (
                          <Button
                            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-4 sm:px-6 text-sm"
                            onClick={() =>
                              handleContinueApplication(application.id)
                            }
                          >
                            Continue
                          </Button>
                        )}
                        <Button
                          className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-4 sm:px-6"
                          onClick={() => handleViewApplication(application.id)}
                        >
                          View
                        </Button>
                        <Button
                          className="bg-red-600 hover:bg-red-700 text-white px-4 sm:px-6"
                          onClick={() =>
                            handleDeleteApplication(application.id)
                          }
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiClientKycDashboard;
