import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CircleFadingPlus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/sonner";

const KycPrompt = () => {
  const navigate = useNavigate();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [clientData, setClientData] = useState({
    email: "",
    phone: "",
    firstName: "",
    lastName: "",
  });

  const handleCompleteKYC = () => {
    // Validate client data
    if (
      !clientData.email ||
      !clientData.phone ||
      !clientData.firstName ||
      !clientData.lastName
    ) {
      toast.error("Please fill in all client information");
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(clientData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    // Validate phone format (basic validation)
    const phoneRegex = /^[+]?([1-9][\d]{0,15})$/;
    if (!phoneRegex.test(clientData.phone.replace(/\s/g, ""))) {
      toast.error("Please enter a valid phone number");
      return;
    }

    // Store client data temporarily for use after verification
    sessionStorage.setItem(
      "pendingClientData",
      JSON.stringify({
        email: clientData.email,
        phone: clientData.phone,
        clientName: `${clientData.firstName} ${clientData.lastName}`,
      })
    );

    // Reset form and close dialog
    setClientData({ email: "", phone: "", firstName: "", lastName: "" });
    setIsDialogOpen(false);

    toast.success(
      `Starting KYC process for ${clientData.firstName} ${clientData.lastName}`
    );

    // Navigate to verification process
    navigate("/kyc/verification-process");
  };

  const handleInputChange = (field: string, value: string) => {
    setClientData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="rounded-xl p-12 text-center relative mt-8">
      <div className="flex flex-col items-center">
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <div className="w-10 h-10 rounded-xl cursor-pointer bg-[#26355E] hover:bg-primary-navy/90 flex items-center justify-center text-white mb-4">
              <CircleFadingPlus size={28} />
            </div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create Your First KYC Application</DialogTitle>
              <DialogDescription>
                Enter the client's information to create a new KYC application.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    value={clientData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    value={clientData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={clientData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+1234567890"
                  value={clientData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCompleteKYC}
                className="bg-[#26355E] hover:bg-[#26355E]/90 text-white"
              >
                Create KYC Application
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <DialogTrigger asChild>
          <Button className="bg-[#26355E] hover:bg-primary-navy/90 text-white py-6 px-12 md:px-44 rounded-lg text-lg font-medium">
            Complete Your KYC
          </Button>
        </DialogTrigger>
      </div>
    </div>
  );
};

export default KycPrompt;
