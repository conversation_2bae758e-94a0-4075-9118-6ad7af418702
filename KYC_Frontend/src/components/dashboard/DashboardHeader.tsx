import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { selectUser } from "@/redux/slices/authSlice";
import { Button } from "@/components/ui/button";
import { Camera } from "lucide-react";

const DashboardHeader = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);

  const handleUploadProfilePicture = () => {
    navigate("/profile");
  };

  return (
    <header className="font-urbanist py-4 px-6 flex items-center justify-between">
      <div className="flex flex-col items-start pt-4">
        <h1 className="text-2xl md:text-5xl font-medium mb-2">
          Welcome,{" "}
          <span className="text-[#AF47D2]">
            {user?.name || user?.firstName || "User Name"}
          </span>
        </h1>
        <p className="text-gray-600">
          Let's complete your KYC in just a few simple steps
        </p>
      </div>
      <div className="flex items-center gap-4">
        {user?.hasSkippedProfilePicture && (
          <Button
            variant="outline"
            onClick={handleUploadProfilePicture}
            className="text-purple border-purple hover:bg-purple/10"
          >
            <Camera size={18} className="mr-2" />
            Add Profile Picture
          </Button>
        )}
      </div>
    </header>
  );
};

export default DashboardHeader;
