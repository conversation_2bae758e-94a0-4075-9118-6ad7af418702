// import React from "react";
// import { useAppSelector } from "@/redux/hooks";
// import {
//   selectIsKYCCompleted,
//   selectKYCCompletionDate,
// } from "@/redux/slices/kycSlice";
// import { format } from "date-fns";
// import { CheckCircle } from "lucide-react";

// const KycStatusTable = () => {
//   const isCompleted = useAppSelector(selectIsKYCCompleted);
//   const completionDate = useAppSelector(selectKYCCompletionDate);

//   if (!isCompleted) {
//     return null;
//   }

//   const formattedDate = completionDate
//     ? format(new Date(completionDate), "dd MMM yyyy")
//     : "N/A";

//   return (
//     <div className="bg-white rounded-xl shadow-sm p-6">
//       <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
//         <div>
//           <h2 className="text-xl font-bold mb-1">KYC Status</h2>
//           <p className="text-gray-500">
//             Your KYC details have been verified. You now have full access to all
//             features.
//           </p>
//         </div>

//         <div className="mt-3 md:mt-0 px-3 py-1 rounded-full bg-green-100 text-green-800 flex items-center">
//           <CheckCircle size={16} className="mr-1" />
//           <span>Verified</span>
//         </div>
//       </div>

//       <div className="overflow-x-auto">
//         <table className="min-w-full text-sm">
//           <thead>
//             <tr className="border-b">
//               <th className="text-left py-3 px-4 text-gray-500 font-medium">
//                 KYC Type
//               </th>
//               <th className="text-left py-3 px-4 text-gray-500 font-medium">
//                 Date Completed
//               </th>
//               <th className="text-left py-3 px-4 text-gray-500 font-medium">
//                 Status
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             <tr>
//               <td className="py-3 px-4">Full KYC</td>
//               <td className="py-3 px-4">{formattedDate}</td>
//               <td className="py-3 px-4">
//                 <span className="px-3 py-1 rounded-full bg-green-100 text-green-800">
//                   Approved
//                 </span>
//               </td>
//             </tr>
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// };

// export default KycStatusTable;

import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import {
  selectKYCCompletionDate,
  selectCompletedApplications,
  switchToApplication,
} from "@/redux/slices/kycSlice";
import { selectUser } from "@/redux/slices/authSlice";
import { Button } from "@/components/ui/button";
import { CircleCheck } from "lucide-react";

const KycStatusTable = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const kycCompletionDate = useAppSelector(selectKYCCompletionDate);
  const completedApplications = useAppSelector(selectCompletedApplications);

  const handleViewApplication = (applicationId: string) => {
    dispatch(switchToApplication(applicationId));
    navigate("/kyc/review");
  };

  return (
    <div className="w-full">
      {/* <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <h2 className="text-xl font-bold">Completed KYC Applications</h2>
        <p className="text-gray-600 mt-1">
          {completedApplications.length} application(s) completed
        </p>
      </div> */}

      <div className="overflow-x-auto rounded-lg border border-gray-200">
        <div className="min-w-[800px]">
          <table className="w-full">
            <thead className="bg-gray-50 rounded-md">
              <tr>
                <th className="text-center py-4 px-2 sm:px-4">Person name</th>
                <th className="text-center py-4 px-2 sm:px-4">
                  Facial verification
                </th>
                <th className="text-center py-4 px-2 sm:px-4">
                  Mobile verification
                </th>
                <th className="text-center py-4 px-2 sm:px-4">
                  Email verification
                </th>
                <th className="text-center py-4 px-2 sm:px-4">KYC</th>
                <th className="text-center py-4 px-2 sm:px-4">
                  Submission Date
                </th>
                <th className="text-center py-4 px-2 sm:px-4">Preview</th>
              </tr>
            </thead>
            <tbody>
              {completedApplications.map((application) => (
                <tr key={application.id}>
                  <td className="py-3 text-center px-2 sm:px-4">
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">
                        {application.clientName}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-2 sm:px-4">
                    <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                      <span>
                        {application.verificationStatus.facial
                          ? "Validated"
                          : "Pending"}
                      </span>
                      <CircleCheck
                        size={16}
                        className={`ml-1 ${
                          application.verificationStatus.facial
                            ? "text-green-500"
                            : "text-gray-400"
                        }`}
                      />
                    </div>
                  </td>
                  <td className="py-3 px-2 sm:px-4">
                    <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                      <span>
                        {application.verificationStatus.mobile
                          ? "Validated"
                          : "Pending"}
                      </span>
                      <span className="text-gray-500 text-xs mt-1">
                        {application.phone}
                      </span>
                      <CircleCheck
                        size={16}
                        className={`ml-1 ${
                          application.verificationStatus.mobile
                            ? "text-green-500"
                            : "text-gray-400"
                        }`}
                      />
                    </div>
                  </td>
                  <td className="py-3 px-2 sm:px-4">
                    <div className="flex flex-col items-center justify-center text-[#AF47D2]">
                      <span>
                        {application.verificationStatus.email
                          ? "Validated"
                          : "Pending"}
                      </span>
                      <span className="text-gray-500 text-xs mt-1">
                        {application.email}
                      </span>
                      <CircleCheck
                        size={16}
                        className={`ml-1 ${
                          application.verificationStatus.email
                            ? "text-green-500"
                            : "text-gray-400"
                        }`}
                      />
                    </div>
                  </td>
                  <td className="py-3 text-center px-2 sm:px-4">
                    <span className="px-2 sm:px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
                      {application.status === "submitted"
                        ? "Under Review"
                        : application.status === "approved"
                        ? "Approved"
                        : "Completed"}
                    </span>
                  </td>
                  <td className="py-3 text-center px-2 sm:px-4">
                    {application.completionDate || application.submissionDate
                      ? new Date(
                          application.completionDate ||
                            application.submissionDate!
                        ).toLocaleDateString()
                      : "-"}
                  </td>
                  <td className="py-3 text-center px-2 sm:px-4">
                    <Button
                      className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-4 sm:px-8"
                      onClick={() => handleViewApplication(application.id)}
                    >
                      View
                    </Button>
                  </td>
                </tr>
              ))}

              {/* Show empty state if no completed applications */}
              {completedApplications.length === 0 && (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    No completed KYC applications yet
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default KycStatusTable;
