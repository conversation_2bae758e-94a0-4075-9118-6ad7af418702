
import React from 'react';
import { cn } from '@/lib/utils';

interface AuthButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'outline';
  isLoading?: boolean;
}

const AuthButton: React.FC<AuthButtonProps> = ({ 
  className, 
  children, 
  variant = 'primary', 
  isLoading = false,
  disabled,
  ...props 
}) => {
  return (
    <button
      className={cn(
        "w-full py-4 px-6 rounded-lg text-center font-medium transition-colors",
        {
          "bg-primary-navy text-white hover:bg-primary-navy/90": variant === 'primary',
          "bg-white text-gray-800 border border-gray-300 hover:bg-gray-50": variant === 'outline',
          "opacity-70 cursor-not-allowed": disabled || isLoading
        },
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <span className="flex items-center justify-center">
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading...
        </span>
      ) : (
        children
      )}
    </button>
  );
};

export default AuthButton;
