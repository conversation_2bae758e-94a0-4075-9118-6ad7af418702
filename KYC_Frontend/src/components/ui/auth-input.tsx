
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';

interface AuthInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
}

const AuthInput: React.FC<AuthInputProps> = ({ 
  className, 
  type = 'text', 
  label,
  error,
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  
  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const isPasswordInput = type === 'password';
  
  return (
    <div className="w-full">
      {label && (
        <label className="block text-gray-700 mb-1">{label}</label>
      )}
      
      <div className="relative">
        <input
          type={isPasswordInput ? (showPassword ? 'text' : 'password') : type}
          className={cn(
            "w-full px-4 py-3 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-light",
            { "border-red-500": error },
            className
          )}
          {...props}
        />
        
        {isPasswordInput && (
          <button
            type="button"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
            onClick={togglePasswordVisibility}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

export default AuthInput;
