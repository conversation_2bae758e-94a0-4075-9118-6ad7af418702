import React, { ReactNode, useState, useEffect } from "react";

interface AuthLayoutProps {
  children: ReactNode;
}

const bannerImages = [
  {
    src: "/Images/Laptop-1.png",
    alt: "Effortless KYC",
    title: "Effortless KYC. Maximum security.",
  },
  {
    src: "/Images/Laptop-2.png",
    alt: "Data Privacy",
    title: "Secure, seamless KYC onboarding anywhere, anytime.",
  },
  {
    src: "/Images/Laptop-3.png",
    alt: "Fast Verification",
    title: "Fast-track your identity with confidence.",
  },
];

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % bannerImages.length);
    }, 3000); // Change slide every 3 seconds

    return () => clearInterval(interval); // Cleanup interval on component unmount
  }, []);

  return (
    <div className="flex min-h-screen font-urbanist">
      {/* Left side - Purple background with laptop image */}
      <div className="hidden lg:flex lg:w-[45%] bg-[#AF47D2] relative flex-col items-center p-10 ">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[26rem] h-[26rem] bg-[#9F2CC5] rounded-full opacity-30"></div>
        <div className="absolute top-28 right-16 w-32 h-32 bg-[#9F2CC5] rounded-full opacity-30"></div>
        {/* Decorative circles */}
        <div className="text-center text-white z-10 w-[60%] mt-16 h-28">
          <h2 className="text-xl font-bold">
            {bannerImages[currentSlide].title}
          </h2>
        </div>
        <div className="z-10 mt-20">
          <div className="">
            <img
              src={bannerImages[currentSlide].src}
              alt={bannerImages[currentSlide].alt}
              className="max-w-lg"
            />
          </div>

          {/* Dots for slide indication */}
          <div className="flex space-x-4 mt-36 items-center justify-center">
            {bannerImages.map((_, index) =>
              index === currentSlide ? (
                <span
                  key={index}
                  className="flex items-center justify-center h-5 w-5 rounded-full border-2 border-white"
                >
                  <span className="h-2 w-2 bg-white rounded-full"></span>
                </span>
              ) : (
                <span
                  key={index}
                  className="h-2 w-2 bg-white rounded-full"
                ></span>
              )
            )}
          </div>
        </div>
      </div>

      {/* Right side - White background with form */}
      <div className="w-full lg:w-[60%] flex flex-col">
        <div className="lg:pt-24 flex justify-center">
          <img
            src="public/Images/logo 1.png"
            alt="Boticx Labs Logo"
            className="h-12"
          />
        </div>
        <div className="flex-grow flex flex-col justify-center px-8 md:px-16 lg:px-24 py-8">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
