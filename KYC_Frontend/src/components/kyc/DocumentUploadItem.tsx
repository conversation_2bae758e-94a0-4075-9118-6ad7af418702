import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Document {
  id: string;
  type: string;
  file: File | null;
  preview: string | null;
  isUploaded: boolean;
  isUploading: boolean;
  uploadFailed?: boolean;
  errorMessage?: string;
}

interface DocumentUploadItemProps {
  doc: Document;
  index: number;
  documentTypes: string[];
  onTypeChange: (index: number, value: string) => void;
  onFileSelect: (index: number) => void;
  onViewDocument: (index: number) => void;
  onRetryUpload?: (index: number) => void;
}

const DocumentUploadItem: React.FC<DocumentUploadItemProps> = ({
  doc,
  index,
  documentTypes,
  onTypeChange,
  onFileSelect,
  onViewDocument,
  onRetryUpload,
}) => {
  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600">Select document to be uploaded</p>

      <div className="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0 md:items-center">
        <div className="flex-grow">
          <Select
            value={doc.type}
            onValueChange={(value) => onTypeChange(index, value)}
            disabled={doc.isUploaded}
          >
            <SelectTrigger className="w-full h-12">
              <SelectValue placeholder="Select document to upload" />
            </SelectTrigger>
            <SelectContent>
              {documentTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {!doc.isUploaded ? (
          <div className="space-y-2">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#AF47D2"
                  strokeWidth="2"
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                </svg>
                <span className="ml-2 text-sm text-gray-600">.jpeg .pdf</span>
              </div>
              <div className="text-xs text-gray-500">max 2 Mb</div>
              <Button
                type="button"
                onClick={() => onFileSelect(index)}
                disabled={!doc.type || doc.isUploading}
                className={`${
                  doc.isUploading
                    ? "bg-gray-300"
                    : "bg-primary-navy hover:bg-primary-navy/90"
                } text-white px-8 py-2 h-12 rounded-md`}
              >
                {doc.isUploading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Uploading...</span>
                  </div>
                ) : (
                  "Upload"
                )}
              </Button>
            </div>

            {/* Error message and retry button */}
            {doc.uploadFailed && doc.errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-center justify-between">
                  <p className="text-red-600 text-xs">{doc.errorMessage}</p>
                  {onRetryUpload && (
                    <Button
                      type="button"
                      onClick={() => onRetryUpload(index)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 border-red-300 hover:bg-red-50 text-xs px-2 py-1 h-6"
                    >
                      Retry
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#AF47D2"
              strokeWidth="2"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
            <span className="text-sm">{doc.file?.name}</span>
            <Button
              type="button"
              onClick={() => onViewDocument(index)}
              className="bg-primary-navy hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
            >
              View
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentUploadItem;
