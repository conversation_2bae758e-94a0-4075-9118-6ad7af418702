import React from "react";
import { Input } from "@/components/ui/input";
import { PersonalInfo } from "@/redux/slices/kycSlice";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface PersonalInfoFormProps {
  formData: PersonalInfo;
  errors: {
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
    dob?: string;
    gender?: string;
    fatherName?: string;
    motherName?: string;
    spouseName?: string;
    dependents?: string;
    guardianName?: string;
    guardianRelation?: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange?: (name: string, value: string) => void;
  handleCheckboxChange?: (name: string, checked: boolean) => void;
  handleNumberChange?: (name: string, value: number) => void;
}

const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  formData,
  errors,
  handleChange,
  handleSelectChange = () => {},
  handleCheckboxChange = () => {},
  handleNumberChange = () => {},
}) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Input
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            placeholder="First name"
            className={`h-12 ${errors.firstName ? "border-red-500" : ""}`}
          />
          {errors.firstName && (
            <p className="mt-1 text-xs text-red-500">{errors.firstName}</p>
          )}
        </div>
        <div>
          <Input
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            placeholder="Last name"
            className={`h-12 ${errors.lastName ? "border-red-500" : ""}`}
          />
          {errors.lastName && (
            <p className="mt-1 text-xs text-red-500">{errors.lastName}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="dob"
            value={formData.dob}
            onChange={handleChange}
            placeholder="DOB: DD/MM/YYYY"
            className={`h-12 ${errors.dob ? "border-red-500" : ""}`}
          />
          {errors.dob && (
            <p className="mt-1 text-xs text-red-500">{errors.dob}</p>
          )}
        </div>
        <div>
          <Select
            value={formData.gender}
            onValueChange={(value) => handleSelectChange("gender", value)}
          >
            <SelectTrigger
              className={`h-12 ${errors.gender ? "border-red-500" : ""}`}
            >
              <SelectValue placeholder="Select Gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          {errors.gender && (
            <p className="mt-1 text-xs text-red-500">{errors.gender}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="fatherName"
            value={formData.fatherName}
            onChange={handleChange}
            placeholder="Father's Name"
            className={`h-12 ${errors.fatherName ? "border-red-500" : ""}`}
          />
          {errors.fatherName && (
            <p className="mt-1 text-xs text-red-500">{errors.fatherName}</p>
          )}
        </div>
        <div>
          <Input
            name="motherName"
            value={formData.motherName}
            onChange={handleChange}
            placeholder="Mother's Name"
            className={`h-12 ${errors.motherName ? "border-red-500" : ""}`}
          />
          {errors.motherName && (
            <p className="mt-1 text-xs text-red-500">{errors.motherName}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <div className="flex items-center bg-[#F7FAFC] rounded-xl border border-[#E2E8F0] px-4 py-3 h-12 justify-between">
            <Label htmlFor="isMarried" className="text-base font-normal">
              Fill checkbox if married
            </Label>
            <Checkbox
              id="isMarried"
              checked={formData.isMarried}
              onCheckedChange={(checked) =>
                handleCheckboxChange("isMarried", checked as boolean)
              }
              className="ml-auto"
            />
          </div>
        </div>
        <div>
          <Input
            name="spouseName"
            value={formData.spouseName}
            onChange={handleChange}
            placeholder="Spouse name"
            className={`h-12 ${errors.spouseName ? "border-red-500" : ""}`}
            disabled={!formData.isMarried}
          />
          {errors.spouseName && (
            <p className="mt-1 text-xs text-red-500">{errors.spouseName}</p>
          )}
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-center bg-[#F7FAFC] rounded-xl border border-[#E2E8F0] px-4 py-3 h-12 w-full">
          <span className="text-base flex-1">No. of dependent</span>
          <button
            type="button"
            className="border rounded-l px-2 py-1 bg-white"
            onClick={() =>
              handleNumberChange(
                "dependents",
                Math.max(0, formData.dependents - 1)
              )
            }
          >
            &lt;
          </button>
          <input
            type="number"
            id="dependents"
            value={formData.dependents}
            onChange={(e) =>
              handleNumberChange("dependents", parseInt(e.target.value) || 0)
            }
            className="border-t border-b text-center w-10 py-1 bg-white"
            min="0"
            style={{ outline: "none" }}
          />
          <button
            type="button"
            className="border rounded-r px-2 py-1 bg-white"
            onClick={() =>
              handleNumberChange("dependents", formData.dependents + 1)
            }
          >
            &gt;
          </button>
        </div>
        {errors.dependents && (
          <p className="mt-1 text-xs text-red-500">{errors.dependents}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="guardianName"
            value={formData.guardianName}
            onChange={handleChange}
            placeholder="Name of guardian"
            className={`h-12 ${errors.guardianName ? "border-red-500" : ""}`}
          />
          {errors.guardianName && (
            <p className="mt-1 text-xs text-red-500">{errors.guardianName}</p>
          )}
        </div>
        <div>
          <Select
            value={formData.guardianRelation}
            onValueChange={(value) =>
              handleSelectChange("guardianRelation", value)
            }
          >
            <SelectTrigger
              className={`h-12 ${
                errors.guardianRelation ? "border-red-500" : ""
              }`}
            >
              <SelectValue placeholder="Relation with guardian" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="parent">Parent</SelectItem>
              <SelectItem value="sibling">Sibling</SelectItem>
              <SelectItem value="relative">Relative</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          {errors.guardianRelation && (
            <p className="mt-1 text-xs text-red-500">
              {errors.guardianRelation}
            </p>
          )}
        </div>
      </div>
    </>
  );
};

export default PersonalInfoForm;
