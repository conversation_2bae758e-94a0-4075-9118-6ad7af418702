import React from "react";
import { Input } from "@/components/ui/input";
import { AdditionalInfo } from  "@/redux/slices/kycSlice";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AdditionalInfoSectionProps {
  additionalInfo: AdditionalInfo;
  editingAdditional: boolean;
  setEditingAdditional: (editing: boolean) => void;
  handleAdditionalChange: (field: string, value: string) => void;
  handleAdditionalCheckboxChange?: (field: string, checked: boolean) => void;
  saveAdditionalInfo: () => void;
}

const AdditionalInfoSection: React.FC<AdditionalInfoSectionProps> = ({
  additionalInfo,
  editingAdditional,
  setEditingAdditional,
  handleAdditionalChange,
  handleAdditionalCheckboxChange = () => {},
  saveAdditionalInfo,
}) => {
  return (
    <div className="border rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          Additional Information
        </h3>
        {!editingAdditional ? (
          <Button
            onClick={() => setEditingAdditional(true)}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            <Edit className="w-4 h-4 mr-1" />
            Edit Additional info
          </Button>
        ) : (
          <Button
            onClick={saveAdditionalInfo}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            Save Changes
          </Button>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Nationality</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.nationality}
              onChange={(e) =>
                handleAdditionalChange("nationality", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.nationality || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Religion</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.religion}
              onChange={(e) =>
                handleAdditionalChange("religion", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.religion || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Country</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.country}
              onChange={(e) =>
                handleAdditionalChange("country", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.country || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Citizenship</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.citizenship}
              onChange={(e) =>
                handleAdditionalChange("citizenship", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.citizenship || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Disability Status</p>
          {editingAdditional ? (
            <div className="flex items-center gap-2 mt-1">
              <Checkbox
                id="hasDisability"
                checked={additionalInfo.hasDisability}
                onCheckedChange={(checked) =>
                  handleAdditionalCheckboxChange(
                    "hasDisability",
                    checked as boolean
                  )
                }
              />
              <Label htmlFor="hasDisability">Has Disability</Label>
            </div>
          ) : (
            <p>{additionalInfo.hasDisability ? "Yes" : "No"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Education</p>
          {editingAdditional ? (
            <Select
              value={additionalInfo.education}
              onValueChange={(value) =>
                handleAdditionalChange("education", value)
              }
            >
              <SelectTrigger className="mt-1 bg-[#F8F9FB]">
                <SelectValue placeholder="Select Education" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="high_school">High School</SelectItem>
                <SelectItem value="bachelors">Bachelor's Degree</SelectItem>
                <SelectItem value="masters">Master's Degree</SelectItem>
                <SelectItem value="phd">PhD</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <p>
              {additionalInfo.education
                ? additionalInfo.education
                    .replace("_", " ")
                    .charAt(0)
                    .toUpperCase() +
                  additionalInfo.education.replace("_", " ").slice(1)
                : "N/A"}
            </p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Occupation</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.occupation}
              onChange={(e) =>
                handleAdditionalChange("occupation", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.occupation || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Organization</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.organization}
              onChange={(e) =>
                handleAdditionalChange("organization", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.organization || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Designation</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.designation}
              onChange={(e) =>
                handleAdditionalChange("designation", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.designation || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Net Worth</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.netWorth}
              onChange={(e) =>
                handleAdditionalChange("netWorth", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.netWorth || "N/A"}</p>
          )}
        </div>
        <div>
          <p className="text-sm text-gray-500">Business Nature</p>
          {editingAdditional ? (
            <Input
              value={additionalInfo.businessNature}
              onChange={(e) =>
                handleAdditionalChange("businessNature", e.target.value)
              }
              className="mt-1 bg-[#F8F9FB]"
            />
          ) : (
            <p>{additionalInfo.businessNature || "N/A"}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfoSection;
