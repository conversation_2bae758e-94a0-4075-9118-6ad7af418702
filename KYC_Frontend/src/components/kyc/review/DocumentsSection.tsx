
import React from "react";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { VerificationStatus } from "@/redux/slices/kycSlice";

interface DocumentsSectionProps {
  verificationStatus: VerificationStatus;
  handleEditDocuments: () => void;
}

const DocumentsSection: React.FC<DocumentsSectionProps> = ({
  verificationStatus,
  handleEditDocuments,
}) => {
  return (
    <div className="border rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          Identity Documents
        </h3>
        <Button
          onClick={handleEditDocuments}
          variant="outline"
          className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
        >
          <Edit className="w-4 h-4 mr-1" />
          Edit Identity Documents
        </Button>
      </div>

      <div className="space-y-4">
        {Object.entries(verificationStatus.documents).length > 0 ? (
          Object.entries(verificationStatus.documents).map(([id, doc]) => (
            <div key={id} className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#AF47D2"
                strokeWidth="2"
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
              </svg>
              <span className="ml-2">{doc.name}</span>
              <Button className="ml-auto text-sm px-3 h-8 bg-[#26355E] text-white" variant="outline">
                View
              </Button>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500">No documents uploaded yet</p>
        )}
      </div>
    </div>
  );
};

export default DocumentsSection;
