
import React from "react";
import { Input } from "@/components/ui/input";
import { AddressDetails } from "@/redux/slices/kycSlice";
import { But<PERSON> } from "@/components/ui/button";
import { Edit } from "lucide-react";

interface AddressSectionProps {
  addressDetails: AddressDetails;
  editingAddress: boolean;
  setEditingAddress: (editing: boolean) => void;
  handleAddressChange: (addressType: 'temporary' | 'permanent', field: string, value: string) => void;
  saveAddressDetails: () => void;
}

const AddressSection: React.FC<AddressSectionProps> = ({
  addressDetails,
  editingAddress,
  setEditingAddress,
  handleAddressChange,
  saveAddressDetails,
}) => {
  return (
    <div className="border rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          Address Details
        </h3>
        {!editingAddress ? (
          <Button
            onClick={() => setEditingAddress(true)}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            <Edit className="w-4 h-4 mr-1" />
            Edit Address Details
          </Button>
        ) : (
          <Button
            onClick={saveAddressDetails}
            variant="outline"
            className="bg-[#26355E] text-white text-sm px-3 py-1 h-auto rounded"
          >
            Save Changes
          </Button>
        )}
      </div>

      <div className="space-y-6">
        <div>
          <p className="text-sm text-gray-500 mb-2">Temporary Address</p>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">
                House no., Street, Landmark
              </p>
              {editingAddress ? (
                <Input
                  value={addressDetails.temporary.street}
                  onChange={(e) => handleAddressChange('temporary', 'street', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.temporary.street}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">City / Town</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.temporary.city}
                  onChange={(e) => handleAddressChange('temporary', 'city', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.temporary.city}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">State / UT</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.temporary.state}
                  onChange={(e) => handleAddressChange('temporary', 'state', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.temporary.state}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">Pincode</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.temporary.pincode}
                  onChange={(e) => handleAddressChange('temporary', 'pincode', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.temporary.pincode}</p>
              )}
            </div>
          </div>
        </div>

        <div>
          <p className="text-sm text-gray-500 mb-2">Permanent Address</p>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">
                House no., Street, Landmark
              </p>
              {editingAddress ? (
                <Input
                  value={addressDetails.permanent.street}
                  onChange={(e) => handleAddressChange('permanent', 'street', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.permanent.street}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">City / Town</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.permanent.city}
                  onChange={(e) => handleAddressChange('permanent', 'city', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.permanent.city}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">State / UT</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.permanent.state}
                  onChange={(e) => handleAddressChange('permanent', 'state', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.permanent.state}</p>
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">Pincode</p>
              {editingAddress ? (
                <Input
                  value={addressDetails.permanent.pincode}
                  onChange={(e) => handleAddressChange('permanent', 'pincode', e.target.value)}
                  className="mt-1 bg-[#F8F9FB]"
                />
              ) : (
                <p>{addressDetails.permanent.pincode}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressSection;
