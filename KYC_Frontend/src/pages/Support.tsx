import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/redux/hooks";
import { selectIsAuthenticated } from "@/redux/slices/authSlice";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";

// Import refactored components
import DashboardSidebar from "@/components/dashboard/DashboardSidebar";
import DashboardHeader from "@/components/dashboard/DashboardHeader";

const Support = () => {
  const navigate = useNavigate();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  const handleConnect = () => {
    toast.success("Support request submitted. We'll contact you shortly.");
  };

  return (
    <div className="min-h-screen flex font-urbanist">
      {/* Sidebar */}
      <DashboardSidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader />

        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-2">Support Center</h2>
                <p className="text-gray-600">
                  We're here to help with any questions or issues you might
                  have.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="p-6 border border-gray-200 rounded-xl">
                  <div className="w-12 h-12 rounded-full bg-purple/10 flex items-center justify-center mb-4">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#AF47D2"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                      <line x1="3" y1="9" x2="21" y2="9" />
                      <line x1="9" y1="21" x2="9" y2="9" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">FAQs</h3>
                  <p className="text-gray-600 mb-4">
                    Find answers to commonly asked questions about our services
                    and features.
                  </p>
                  <Button
                    variant="outline"
                    className="border-[#AF47D21A] text-[#AF47D2] hover:bg-purple/10"
                  >
                    Browse FAQs
                  </Button>
                </div>

                <div className="p-6 border border-gray-200 rounded-xl">
                  <div className="w-12 h-12 rounded-full bg-purple/10 flex items-center justify-center mb-4">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#AF47D2"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Contact Support
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Get in touch with our support team for personalized
                    assistance.
                  </p>
                  <Button
                    onClick={handleConnect}
                    className="bg-[#26355E] hover:bg-primary-navy/90 text-white"
                  >
                    Connect
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Support;
