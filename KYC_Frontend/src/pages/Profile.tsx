import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectUser, updateUser, logout } from "@/redux/slices/authSlice";
import DashboardSidebar from "@/components/dashboard/DashboardSidebar";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Camera, LogOut, X } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { authService } from "@/services/authService";
import { User } from "@/types/user";

const Profile = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name:
      user?.name || `${user?.firstName || ""} ${user?.lastName || ""}`.trim(),
    email: user?.email || "",
    phone_no: user?.phone_no || "",
    gender: user?.gender || "Male",
  });

  const [profilePicture, setProfilePicture] = useState({
    file: null as File | null,
    preview: user?.profile_pic || user?.profilePicture || "",
    current: user?.profile_pic || user?.profilePicture || "",
  });

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone_no: "",
  });

  // Update profile picture state when user data changes
  useEffect(() => {
    if (user) {
      const profilePicUrl = user.profile_pic || user.profilePicture || "";

      setProfilePicture((prev) => ({
        ...prev,
        preview: profilePicUrl,
        current: profilePicUrl,
      }));

      setFormData({
        name:
          user.name || `${user.firstName || ""} ${user.lastName || ""}`.trim(),
        email: user.email || "",
        phone_no: user.phone_no || "",
        gender: user.gender || "Male",
      });
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const handleProfilePictureChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Please select a valid image file");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicture({
          file,
          preview: e.target?.result as string,
          current: profilePicture.current,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveProfilePicture = () => {
    setProfilePicture({
      file: null,
      preview: "",
      current: profilePicture.current,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      email: "",
      phone_no: "",
    };

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
      isValid = false;
    }

    if (!formData.phone_no.trim()) {
      newErrors.phone_no = "Phone number is required";
      isValid = false;
    } else if (!/^\d{10}$/.test(formData.phone_no.replace(/\D/g, ""))) {
      newErrors.phone_no = "Please enter a valid 10-digit phone number";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleEditProfile = () => {
    setIsEditing(true);
  };

  const handleSaveChanges = async () => {
    if (!user?.user_id && !user?.id) return;

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const userId = user.user_id || user.id!;

      const updates: Partial<User> = {
        name: formData.name,
        email: formData.email,
        phone_no: formData.phone_no,
        gender: formData.gender,
      };

      // Use the updated updateProfile method that handles both text and file uploads
      const updatedUser = await authService.updateProfile(
        userId,
        updates,
        profilePicture.file || undefined
      );

      dispatch(updateUser(updatedUser));

      // Update local state with new profile picture
      setProfilePicture({
        file: null,
        preview: updatedUser.profile_pic || "",
        current: updatedUser.profile_pic || "",
      });

      setIsEditing(false);
      toast.success("Profile updated successfully");
    } catch (error) {
      console.error("Profile update error:", error);

      // Handle different types of errors
      let errorMessage = "Failed to update profile";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      } else if (error && typeof error === "object" && "message" in error) {
        errorMessage = (error as { message: string }).message;
      }

      // Show the error message from backend
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset form data to original values
    setFormData({
      name:
        user?.name || `${user?.firstName || ""} ${user?.lastName || ""}`.trim(),
      email: user?.email || "",
      phone_no: user?.phone_no || "",
      gender: user?.gender || "Male",
    });

    // Reset profile picture to current
    setProfilePicture({
      file: null,
      preview: user?.profile_pic || user?.profilePicture || "",
      current: user?.profile_pic || user?.profilePicture || "",
    });

    // Clear errors and exit edit mode
    setErrors({
      name: "",
      email: "",
      phone_no: "",
    });

    setIsEditing(false);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleChangePassword = () => {
    navigate("/change-password");
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      dispatch(logout());
      toast.success("Logged out successfully");
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout");
    }
  };

  return (
    <div className="flex font-urbanist">
      <DashboardSidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader />

        <main className="flex-1 overflow-y-auto w-full p-4 sm:p-6">
          <div className="max-w-7xl mx-auto">
            {!isEditing ? (
              // Profile View Mode
              <div className="bg-white rounded-xl p-4 sm:p-8">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-8">
                  <div className="flex items-center gap-4">
                    <Avatar
                      className="w-16 h-16 sm:w-24 sm:h-24"
                      key={
                        user?.profile_pic || user?.profilePicture || "no-pic"
                      }
                    >
                      {user?.profile_pic || user?.profilePicture ? (
                        <AvatarImage
                          src={user.profile_pic || user.profilePicture}
                          alt="Profile"
                          onError={(e) => {
                            e.currentTarget.style.display = "none";
                          }}
                        />
                      ) : (
                        <AvatarFallback className="bg-[#AF47D2] text-white text-xl sm:text-2xl">
                          {user?.name?.[0] || user?.firstName?.[0] || "U"}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className="font-semibold text-lg sm:text-xl">
                      {user?.name ||
                        `${user?.firstName || ""} ${
                          user?.lastName || ""
                        }`.trim() ||
                        "User"}
                    </span>
                  </div>
                  <Button
                    onClick={handleEditProfile}
                    className="bg-[#26355E] text-white hover:bg-primary-navy/90 w-full sm:w-auto"
                  >
                    Edit Profile
                  </Button>
                </div>

                <div className="flex flex-col md:flex-row items-center gap-8 mb-16">
                  <div className="flex-1 space-y-6 w-full">
                    <div className="flex flex-col md:flex-row gap-6 mb-4">
                      <div className="w-full md:w-1/2">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-8 h-8 rounded-full bg-purple/10 flex items-center justify-center">
                            <svg
                              width="18"
                              height="18"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-[#AF47D2]"
                            >
                              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                              <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                          </div>
                          <span className="text-gray-600">Email Address</span>
                        </div>
                        <p className="font-medium text-base sm:text-lg pl-11">
                          {user?.email}
                        </p>
                      </div>
                      <div className="w-full md:w-1/2">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-8 h-8 rounded-full bg-purple/10 flex items-center justify-center">
                            <svg
                              width="18"
                              height="18"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-[#AF47D2]"
                            >
                              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                            </svg>
                          </div>
                          <span className="text-gray-600">Mobile Number</span>
                        </div>
                        <p className="font-medium text-base sm:text-lg pl-11">
                          {user?.phone_no}
                        </p>
                      </div>
                    </div>

                    <Button
                      onClick={handleChangePassword}
                      className="bg-[#26355E] text-white hover:bg-primary-navy/90 w-full sm:w-auto"
                    >
                      Change Password
                    </Button>
                  </div>
                </div>

                <div className="pt-8 sm:pt-16 border-t">
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    className="border-[#26355E] text-[#26355E] hover:bg-[#26355E]/10 w-full sm:w-auto"
                  >
                    Log out
                    <LogOut size={18} className="ml-2" />
                  </Button>
                </div>
              </div>
            ) : (
              // Profile Edit Mode
              <div className="p-4 sm:p-8">
                <h1 className="text-xl sm:text-2xl font-semibold mb-8 text-center">
                  Edit Profile
                </h1>

                <div className="flex flex-col items-center mb-8">
                  <div className="relative">
                    <Avatar
                      className="w-24 h-24 sm:w-32 sm:h-32 mb-4"
                      key={profilePicture.preview || "no-preview"}
                    >
                      {profilePicture.preview ? (
                        <AvatarImage
                          src={profilePicture.preview}
                          alt="Profile"
                          onError={(e) => {
                            e.currentTarget.style.display = "none";
                          }}
                        />
                      ) : (
                        <AvatarFallback className="bg-[#AF47D2] text-white text-3xl sm:text-4xl">
                          {user?.name?.[0] || user?.firstName?.[0] || "U"}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    {profilePicture.preview && (
                      <Button
                        onClick={handleRemoveProfilePicture}
                        variant="outline"
                        size="sm"
                        className="absolute -top-2 -right-2 h-8 w-8 rounded-full p-0 border-red-500 text-red-500 hover:bg-red-50"
                      >
                        <X size={12} />
                      </Button>
                    )}
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureChange}
                    className="hidden"
                  />

                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="mt-2 border-[#26355E] text-white bg-[#26355E] w-full sm:w-auto"
                  >
                    <Camera size={18} className="mr-2" />
                    {profilePicture.preview ? "Change" : "Upload"} profile
                    picture
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 max-w-[70%] mx-auto">
                  <div className="md:col-span-2">
                    <label className="block text-gray-600 mb-2">
                      Full Name
                    </label>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`bg-[#F8F9FB] ${
                        errors.name ? "border-red-500" : ""
                      }`}
                      placeholder="Enter your full name"
                    />
                    {errors.name && (
                      <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-600 mb-2">
                      Email Address
                    </label>
                    <Input
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`bg-[#F8F9FB] ${
                        errors.email ? "border-red-500" : ""
                      }`}
                      placeholder="Enter your email"
                      type="email"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.email}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-gray-600 mb-2">
                      Mobile Number
                    </label>
                    <Input
                      name="phone_no"
                      value={formData.phone_no}
                      onChange={handleInputChange}
                      className={`bg-[#F8F9FB] ${
                        errors.phone_no ? "border-red-500" : ""
                      }`}
                      placeholder="Enter your mobile number"
                      type="tel"
                    />
                    {errors.phone_no && (
                      <p className="text-red-500 text-sm mt-1">
                        {errors.phone_no}
                      </p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-gray-600 mb-2">Gender</label>
                    <select
                      name="gender"
                      value={formData.gender}
                      onChange={(e) =>
                        setFormData({ ...formData, gender: e.target.value })
                      }
                      className="w-full px-4 py-3 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-light"
                    >
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mb-8">
                  <Button
                    onClick={handleSaveChanges}
                    className="bg-[#26355E] text-white hover:bg-primary-navy/90 w-full sm:w-auto"
                    disabled={isLoading}
                  >
                    {isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                  <Button
                    onClick={handleCancelEdit}
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 w-full sm:w-auto"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Profile;
