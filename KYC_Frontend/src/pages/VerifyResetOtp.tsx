import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/redux/hooks";
import { selectForgotPasswordEmail } from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import OTPInput from "@/components/ui/otp-input";
import AuthButton from "@/components/ui/auth-button";
import { authService } from "@/services/authService";
import { toast } from "@/components/ui/sonner";

const VerifyResetOtp = () => {
  const navigate = useNavigate();
  const forgotPasswordEmail = useAppSelector(selectForgotPasswordEmail);

  const [verificationCode, setVerificationCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (!forgotPasswordEmail) {
      // Redirect if no email is available
      navigate("/forgot-password");
    }
  }, [forgotPasswordEmail, navigate]);

  useEffect(() => {
    // Countdown timer for resend
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const sendOtp = async () => {
    if (!forgotPasswordEmail) return;

    setIsLoading(true);
    try {
      await authService.sendOtp(forgotPasswordEmail, "email");
      toast.success(`OTP sent to ${forgotPasswordEmail}`);
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = () => {
    if (canResend) {
      sendOtp();
    }
  };

  const handleVerify = async () => {
    if (!forgotPasswordEmail) return;
    if (verificationCode.length !== 6) {
      toast.error("Please enter the complete verification code");
      return;
    }

    setIsLoading(true);

    try {
      const isValid = await authService.verifyOtp(
        forgotPasswordEmail,
        verificationCode,
        "email"
      );

      if (isValid) {
        toast.success("Verification successful");
        navigate("/reset-password");
      } else {
        toast.error("Invalid verification code");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      toast.error("Verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-xl mx-auto w-full text-center">
        <h1 className="text-3xl font-bold mb-2">OTP Verification</h1>
        <p className="text-gray-600 mb-10">
          Enter the verification code we just sent on your email address.
        </p>

        <OTPInput
          length={6}
          onComplete={setVerificationCode}
          className="mb-8"
        />

        <AuthButton
          onClick={handleVerify}
          isLoading={isLoading}
          className="mb-6"
        >
          Verify
        </AuthButton>

        <div className="text-gray-600">
          Didn't received code?{" "}
          <button
            disabled={!canResend || isLoading}
            onClick={handleResend}
            className={`text-purple font-medium ${
              !canResend ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {canResend ? "Resend" : `Resend in ${countdown}s`}
          </button>
        </div>
      </div>
    </AuthLayout>
  );
};

export default VerifyResetOtp;
