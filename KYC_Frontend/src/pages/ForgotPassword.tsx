
import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAppDispatch } from '@/redux/hooks';
import { setForgotPasswordEmail } from '@/redux/slices/authSlice';
import AuthLayout from '@/components/AuthLayout';
import AuthInput from '@/components/ui/auth-input';
import AuthButton from '@/components/ui/auth-button';
import { authService } from '@/services/authService';
import { toast } from '@/components/ui/sonner';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = () => {
    if (!email.trim()) {
      setError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }
    setError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateEmail()) return;
    
    setIsLoading(true);
    
    try {
      // Send OTP to email
      await authService.sendOtp(email, 'email');
      
      // Store email for verification
      dispatch(setForgotPasswordEmail(email));
      
      toast.success(`Verification code sent to ${email}`);
      navigate('/verify-reset-otp');
    } catch (error) {
      console.error('Forgot password error:', error);
      toast.error('Failed to send verification code');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-lg mx-auto w-full">
        <h1 className="text-3xl font-bold mb-2">Forgot Password?</h1>
        <p className="text-gray-600 mb-8">
          Don't worry! It occurs. Please enter the email address linked with your account.
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <AuthInput
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            error={error}
          />
          
          <AuthButton type="submit" isLoading={isLoading}>
            Send code
          </AuthButton>
        </form>
        
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Remember Password?{' '}
            <Link to="/login" className="text-purple font-medium">
              Login Now
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
};

export default ForgotPassword;
