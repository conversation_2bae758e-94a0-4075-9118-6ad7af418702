import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setCurrentStep,
  selectVerificationStatus,
  selectPersonalInfo,
  selectAddressDetails,
  selectAdditionalInfo,
  saveCurrentApplication,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";

const Summary = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const verificationStatus = useAppSelector(selectVerificationStatus);
  const personalInfo = useAppSelector(selectPersonalInfo);
  const addressDetails = useAppSelector(selectAddressDetails);
  const additionalInfo = useAppSelector(selectAdditionalInfo);

  const [allVerified, setAllVerified] = useState(true);

  useEffect(() => {
    dispatch(setCurrentStep(8)); // Step 8: Summary

    // Check if all documents and facial verification are completed
    const docsVerified = Object.values(verificationStatus.documents).every(
      (doc) => doc.verified
    );
    const allVerified = docsVerified && verificationStatus.facial;
    setAllVerified(allVerified);
  }, [dispatch, verificationStatus]);

  const handleBack = () => {
    navigate("/kyc/verification");
  };

  const handleNext = () => {
    if (allVerified) {
      dispatch(setCurrentStep(9)); // Move to step 9 (Review)
      dispatch(saveCurrentApplication()); // Save progress
      navigate("/kyc/review");
    } else {
      navigate("/kyc/re-verification");
    }
  };

  const renderVerificationStatus = (isVerified: boolean) =>
    isVerified ? (
      <div className="py-3 text-left">
        <span className="px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
          Verified
        </span>
      </div>
    ) : (
      <div className="py-3 text-left">
        <span className="px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
          Not Verified
        </span>
      </div>
    );

  return (
    <KYCLayout
      title="Documents Verification Summary"
      subtitle="Here's what we found and verified from your uploaded documents."
    >
      <div className="space-y-6">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">
                  Fields
                </th>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">
                  Value
                </th>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">
                  Verification Status
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="px-4 py-3">Name</td>
                <td className="px-4 py-3">
                  {personalInfo.firstName} {personalInfo.lastName}
                </td>
                <td className="px-4 py-3">{renderVerificationStatus(true)}</td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Email</td>
                <td className="px-4 py-3">{personalInfo.email}</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.email)}
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Mobile</td>
                <td className="px-4 py-3">{personalInfo.mobile}</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.mobile)}
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Address</td>
                <td className="px-4 py-3">
                  {addressDetails.permanent.street},{" "}
                  {addressDetails.permanent.city}
                </td>
                <td className="px-4 py-3">{renderVerificationStatus(true)}</td>
              </tr>
              {Object.entries(verificationStatus.documents).map(
                ([key, doc]) => (
                  <tr key={key} className="border-b">
                    <td className="px-4 py-3">Document ({doc.type})</td>
                    <td className="px-4 py-3">{doc.name}</td>
                    <td className="px-4 py-3">
                      {renderVerificationStatus(doc.verified)}
                    </td>
                  </tr>
                )
              )}
              <tr className="border-b">
                <td className="px-4 py-3">Face</td>
                <td className="px-4 py-3">Scanned Photo</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.facial)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            onClick={handleNext}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            {allVerified ? "Proceed" : "Re-verification"}
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default Summary;
