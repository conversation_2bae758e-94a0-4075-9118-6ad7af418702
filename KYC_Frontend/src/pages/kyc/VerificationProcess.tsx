import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import { setCurrentStep, updatePersonalInfo } from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import { kycService } from "@/services/kycService";
import { useDuplicateValidation } from "@/hooks/useDuplicateValidation";
import { useAppSelector } from "@/redux/hooks";
// import { fetchKYCApplications } from "@/redux/slices/kycSlice";

const VerificationProcess = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  // Get user_id from Redux store (adjust selector as per your store structure)
  const user = useAppSelector((state) => state.auth.user);
  // console.log("Redux user object:", user);
  const user_id = user?.user_id;
  // console.log(user_id);

  // Form state - Start with empty fields for new client data entry
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [emailError, setEmailError] = useState("");
  const [mobileError, setMobileError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Duplicate validation hook
  const {
    isValidating,
    validationError,
    hasDuplicates,
    duplicateStatus,
    validateDuplicates,
    clearValidation,
  } = useDuplicateValidation();

  useEffect(() => {
    dispatch(setCurrentStep(1));
  }, [dispatch]);

  // Trigger duplicate validation when both fields have valid values
  useEffect(() => {
    if (email && mobile && email.includes("@") && mobile.length === 10) {
      validateDuplicates(email, mobile);
    } else {
      clearValidation();
    }
  }, [email, mobile, validateDuplicates, clearValidation]);

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError("Email is required");
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    } else {
      setEmailError("");
      return true;
    }
  };

  // Validate mobile number
  const validateMobile = (mobile: string) => {
    if (!mobile) {
      setMobileError("Mobile number is required");
      return false;
    } else if (mobile.length !== 10 || !/^\d+$/.test(mobile)) {
      setMobileError("Please enter a valid 10-digit mobile number");
      return false;
    } else {
      setMobileError("");
      return true;
    }
  };

  // Handle proceed to verification
  const handleProceed = async () => {
    // Validate both email and mobile
    const isEmailValid = validateEmail(email);
    const isMobileValid = validateMobile(mobile);

    if (isEmailValid && isMobileValid) {
      // Set submitting state to show loading indicator
      setIsSubmitting(true);

      try {
        // Create onboarding user with email and phone
        const response = await kycService.createOnboardingUser(
          email,
          mobile,
          user_id
        );
        console.log("Onboarding user created:", response);

        if (response.success) {
          // Update email and mobile in redux store
          dispatch(
            updatePersonalInfo({
              email,
              mobile,
              onboardingUserId: response.id, // Store the onboarding user ID for future reference
            })
          );
          // Fetch latest applications for dashboard
          // dispatch(fetchKYCApplications()); // <-- Add this line

          toast.success("Client information saved successfully");

          // Navigate to the contact verification page
          navigate("/kyc/contact-verification");
        } else {
          toast.error("Failed to save client information");
        }
      } catch (error) {
        console.error("Error creating onboarding user:", error);
        toast.error(
          error instanceof Error
            ? error.message
            : "Failed to save client information. Please try again."
        );
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Show error toast if validation fails
      toast.error("Please provide valid email and mobile number");
    }
  };

  return (
    <KYCLayout
      title="Verification Process"
      subtitle="Enter client contact information for verification"
    >
      <div className="space-y-8 max-w-md mx-auto">
        {/* Email input */}
        <div className="space-y-2">
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700"
          >
            Email Address
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border bg-[#F7FAFC] border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-light"
            placeholder="Enter client's email address"
          />
          {emailError && <p className="text-red-500 text-sm">{emailError}</p>}
          {duplicateStatus.emailExists && (
            <p className="text-red-500 text-sm">
              This email is already registered. Please use a different email.
            </p>
          )}
        </div>

        {/* Mobile input */}
        <div className="space-y-2">
          <label
            htmlFor="mobile"
            className="block text-sm font-medium text-gray-700"
          >
            Mobile Number
          </label>
          <input
            type="tel"
            id="mobile"
            value={mobile}
            onChange={(e) => setMobile(e.target.value)}
            className="w-full px-3 py-2 border bg-[#F7FAFC] border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-light"
            placeholder="Enter client's 10-digit mobile number"
          />
          {mobileError && <p className="text-red-500 text-sm">{mobileError}</p>}
          {duplicateStatus.phoneExists && (
            <p className="text-red-500 text-sm">
              This phone number is already registered. Please use a different
              phone number.
            </p>
          )}
        </div>

        {/* Validation status */}
        {isValidating && (
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
              <p className="text-sm text-yellow-800">
                Checking for duplicate entries...
              </p>
            </div>
          </div>
        )}

        {validationError && (
          <div className="bg-red-50 p-3 rounded-lg border border-red-200">
            <p className="text-sm text-red-800">{validationError}</p>
          </div>
        )}

        {/* Security notice */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
          <div className="flex items-start">
            <div className="text-blue-600 mr-2 mt-0.5">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </div>
            <p className="text-sm text-blue-800">
              Client information is secure and will only be used for
              verification purposes. The process typically takes 5-10 minutes to
              complete.
            </p>
          </div>
        </div>

        {/* Proceed button */}
        <div className="flex justify-end space-x-4">
          <Button
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleProceed}
            disabled={isSubmitting || hasDuplicates || isValidating}
          >
            {isSubmitting
              ? "Processing..."
              : hasDuplicates
              ? "Cannot Proceed - Duplicates Found"
              : isValidating
              ? "Validating..."
              : "Proceed"}
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default VerificationProcess;
