import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import {
  verifyFacial,
  setCurrentStep,
  saveCurrentApplication,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { Camera } from "lucide-react";
import { toast } from "@/components/ui/sonner";

const FacialVerification = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const [cameraActive, setCameraActive] = useState(true); // false
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [scanStatus, setScanStatus] = useState<
    "idle" | "scanning" | "success" | "error"
  >("idle");

  useEffect(() => {
    dispatch(setCurrentStep(5)); // Step 5: Facial Verification
  }, [dispatch]);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "user",
          width: { ideal: 640 },
          height: { ideal: 480 },
        },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      toast.error("Could not access camera. Please check permissions.");
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      const tracks = stream.getTracks();
      tracks.forEach((track) => track.stop());
      videoRef.current.srcObject = null;
      setCameraActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext("2d");
      if (context) {
        canvasRef.current.width = videoRef.current.videoWidth;
        canvasRef.current.height = videoRef.current.videoHeight;
        context.drawImage(
          videoRef.current,
          0,
          0,
          canvasRef.current.width,
          canvasRef.current.height
        );

        const imageData = canvasRef.current.toDataURL("image/png");
        setCapturedImage(imageData);
        stopCamera();

        // Store image in localStorage
        localStorage.setItem("kyc_facial_image", imageData);

        // Simulate facial verification
        scanFace(imageData);
      }
    }
  };

  const scanFace = (imageData: string) => {
    setScanStatus("scanning");

    // Simulate face scanning process with a realistic delay
    setTimeout(() => {
      // Mock successful verification
      dispatch(verifyFacial(true));
      dispatch(setCurrentStep(6)); // Move to step 6 (Documents)
      dispatch(saveCurrentApplication()); // Save progress
      setScanStatus("success");
      toast.success("Facial verification successful");

      // Navigate to next step after a brief delay
      setTimeout(() => {
        navigate("/kyc/documents");
      }, 1500);
    }, 2000);
  };

  const handleBack = () => {
    stopCamera();
    navigate("/kyc/address");
  };

  return (
    <KYCLayout
      title="Facial Verification"
      subtitle="One quick scan to ensure it's really you."
    >
      <div className="space-y-8 flex flex-col items-center">
        <div className="w-64 h-64 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
          {cameraActive ? (
            <video
              ref={videoRef}
              autoPlay
              playsInline
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          ) : capturedImage ? (
            <img
              src={capturedImage}
              alt="Captured face"
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          ) : (
            <Camera size={64} className="text-gray-400" />
          )}

          <canvas ref={canvasRef} style={{ display: "none" }} />
        </div>

        {scanStatus === "scanning" ? (
          <div className="text-center">
            <div className="inline-block w-6 h-6 border-2 border-t-purple border-r-purple border-b-transparent border-l-transparent rounded-full animate-spin"></div>
            <p className="mt-2 text-gray-600">Scanning your face...</p>
          </div>
        ) : scanStatus === "success" ? (
          <div className="text-center flex justify-center items-center gap-2">
            <p className="mt-0 text-green-500">Verification successful</p>
            <div className="w-4 h-4 bg-green-500 text-white rounded-full flex items-center justify-center">
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </div>
          </div>
        ) : (
          <Button
            onClick={cameraActive ? captureImage : startCamera}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            {cameraActive ? "Scan to verify" : "Start camera"}
          </Button>
        )}

        <div className="flex justify-between w-full pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            disabled={scanStatus === "scanning"}
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default FacialVerification;
