import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  verifyMobile,
  setCurrentStep,
  selectPersonalInfo,
} from "@/redux/slices/kycSlice";
import { selectUser, updateUser } from "@/redux/slices/authSlice";
import { authService } from "@/services/authService";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import OTPInput from "@/components/ui/otp-input";
import { toast } from "@/components/ui/sonner";

const VerifyMobile = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const personalInfo = useAppSelector(selectPersonalInfo);
  const user = useAppSelector(selectUser);

  const [otp, setOtp] = useState("");
  const [generatedOtp, setGeneratedOtp] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  useEffect(() => {
    dispatch(setCurrentStep(5));
    // Generate and send OTP on page load
    generateAndSendOtp();
  }, [dispatch, personalInfo.mobile]);

  const generateAndSendOtp = () => {
    // Generate a 6-digit OTP
    const newOtp = Math.floor(100000 + Math.random() * 900000).toString();
    setGeneratedOtp(newOtp);

    // Display the OTP in an alert for testing
    toast.info(`OTP sent to ${personalInfo.mobile}: ${newOtp}`);
  };

  const handleOtpComplete = (code: string) => {
    setOtp(code);
  };

  const handleChangeMobile = () => {
    navigate("/kyc/personal");
  };

  const handleResendOtp = () => {
    setIsResending(true);
    // Regenerate OTP
    setTimeout(() => {
      generateAndSendOtp();
      setIsResending(false);
    }, 1000);
  };

  const handleBack = () => {
    navigate("/kyc/verify-email");
  };

  const handleVerify = async () => {
    if (otp.length !== 6) {
      toast.error("Please enter a valid OTP");
      return;
    }

    setIsVerifying(true);
    // Verify OTP
    setTimeout(async () => {
      if (otp === generatedOtp) {
        dispatch(verifyMobile(true));

        // Update user's phone verification status
        if (user?.user_id || user?.id) {
          try {
            const userId = user.user_id || user.id!;
            const updatedUser = await authService.updateProfile(userId, {
              isphoneverified: true,
            });
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        toast.success("Mobile number verified successfully");
        navigate("/kyc/personal");
      } else {
        toast.error("Invalid OTP. Please try again.");
      }
      setIsVerifying(false);
    }, 1500);
  };

  return (
    <KYCLayout
      title="Verify Your Contact Info"
      subtitle="Enter the OTP sent to your mobile number."
    >
      <div className="space-y-8 max-w-md mx-auto">
        <div>
          <h3 className="text-xl font-medium text-center mb-2">Mobile OTP</h3>
          <div className="flex justify-center items-center space-x-2 text-sm">
            <span className="text-gray-600">Want to change mobile number?</span>
            <button
              onClick={handleChangeMobile}
              className="text-purple hover:underline font-medium"
            >
              Change mobile number
            </button>
          </div>
        </div>

        <div className="flex justify-center">
          <OTPInput length={6} onComplete={handleOtpComplete} />
        </div>

        <div className="flex justify-center items-center space-x-2 text-sm">
          <span className="text-gray-600">Didn't received code?</span>
          <button
            onClick={handleResendOtp}
            disabled={isResending}
            className="text-purple hover:underline font-medium disabled:text-gray-400"
          >
            {isResending ? "Sending..." : "Resend"}
          </button>
        </div>

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            onClick={handleVerify}
            disabled={otp.length !== 6 || isVerifying}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            {isVerifying ? "Verifying..." : "Verify"}
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default VerifyMobile;
