
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setCurrentStep, selectVerificationStatus } from '@/redux/slices/kycSlice';
import KYCLayout from '@/components/KYCLayout';
import { Button } from '@/components/ui/button';

const ReVerification = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const verificationStatus = useAppSelector(selectVerificationStatus);

  useEffect(() => {
    dispatch(setCurrentStep(10));
  }, [dispatch]);

  const handleBack = () => {
    navigate('/kyc/summary');
  };

  const renderVerificationStatus = (isVerified: boolean) => (
    isVerified ? (
      <div className="px-4 py-1 rounded-full bg-green-100 text-green-700 text-sm">
        Verified
      </div>
    ) : (
      <div className="px-4 py-1 rounded-full bg-red-100 text-red-700 text-sm">
        Not verified
      </div>
    )
  );

  // Navigate to the appropriate verification step based on what's not verified
  const handleReVerify = () => {
    // Check email verification
    if (!verificationStatus.email) {
      navigate('/kyc/verify-email');
      return;
    }
    
    // Check mobile verification
    if (!verificationStatus.mobile) {
      navigate('/kyc/verify-mobile');
      return;
    }
    
    // Check facial verification
    if (!verificationStatus.facial) {
      navigate('/kyc/facial');
      return;
    }
    
    // Check document verifications
    const unverifiedDocId = Object.entries(verificationStatus.documents)
      .find(([_, doc]) => !doc.verified)?.[0];
    
    if (unverifiedDocId) {
      navigate('/kyc/verification');
      return;
    }
    
    // If everything is verified, move to the next step
    navigate('/kyc/review');
  };

  return (
    <KYCLayout 
      title="Documents Verification Summary" 
      subtitle="Here's what we found and verified from your uploaded documents."
    >
      <div className="space-y-6">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">Fields</th>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">Value</th>
                <th className="px-4 py-3 text-left bg-gray-50 text-gray-700 font-medium">Verification Status</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="px-4 py-3">Email</td>
                <td className="px-4 py-3">-</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.email)}
                </td>
              </tr>
              <tr className="border-b">
                <td className="px-4 py-3">Mobile</td>
                <td className="px-4 py-3">-</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.mobile)}
                </td>
              </tr>
              {Object.entries(verificationStatus.documents).map(([key, doc]) => (
                <tr key={key} className="border-b">
                  <td className="px-4 py-3">Document ({doc.type})</td>
                  <td className="px-4 py-3">{doc.name}</td>
                  <td className="px-4 py-3">
                    {renderVerificationStatus(doc.verified)}
                  </td>
                </tr>
              ))}
              <tr className="border-b">
                <td className="px-4 py-3">Face</td>
                <td className="px-4 py-3">Scanned Photo</td>
                <td className="px-4 py-3">
                  {renderVerificationStatus(verificationStatus.facial)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="flex justify-between pt-6">
          <Button 
            type="button" 
            onClick={handleBack}
            variant="outline" 
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button 
            onClick={handleReVerify}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            Re-verification
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default ReVerification;
