import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setCurrentStep,
  verifyEmail,
  verifyMobile,
  selectPersonalInfo,
  saveCurrentApplication,
  selectCurrentApplicationId,
  selectKYCApplications,
  createNewApplication,
} from "@/redux/slices/kycSlice";
import { selectUser, updateUser } from "@/redux/slices/authSlice";
import { triggerDashboardRefresh } from "@/redux/slices/kycSlice";
import { authService } from "@/services/authService";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import OTPInput from "@/components/ui/otp-input";
import { toast } from "@/components/ui/sonner";
import { kycService } from "@/services/kycService";

const ContactVerification = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const personalInfo = useAppSelector(selectPersonalInfo);
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);
  const user = useAppSelector(selectUser);

  // OTP state
  const [emailOTP, setEmailOTP] = useState("");
  const [mobileOTP, setMobileOTP] = useState("");

  // Status state
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isMobileVerified, setIsMobileVerified] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const [isResendingMobile, setIsResendingMobile] = useState(false);
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [isVerifyingMobile, setIsVerifyingMobile] = useState(false);

  useEffect(() => {
    dispatch(setCurrentStep(2));
    // Generate and send email OTP on page load
    generateEmailOTP();
  }, [dispatch]);

  // Generate and send email OTP
  const generateEmailOTP = async () => {
    try {
      await kycService.sendKYCEmailOtp(personalInfo.email);
      toast.success(`OTP sent to ${personalInfo.email}`);
    } catch (error) {
      console.error("Error sending email OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send email OTP. Please try again."
      );
    }
  };

  // Generate and send mobile OTP
  const generateMobileOTP = async () => {
    try {
      await kycService.sendKYCPhoneOtp(personalInfo.mobile);
      toast.success(`OTP sent to ${personalInfo.mobile}`);
    } catch (error) {
      console.error("Error sending mobile OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send mobile OTP. Please try again."
      );
    }
  };

  // Handle email verification
  const handleVerifyEmail = async () => {
    if (emailOTP.length !== 6) {
      toast.error("Please enter a valid OTP");
      return;
    }

    setIsVerifyingEmail(true);
    try {
      const isValid = await kycService.verifyKYCEmailOtp(
        personalInfo.email,
        emailOTP
      );

      if (isValid) {
        setIsEmailVerified(true);
        dispatch(verifyEmail(true));

        // Update user's email verification status
        if (user?.user_id || user?.id) {
          try {
            const userId = user.user_id || user.id!;
            const updatedUser = await authService.updateProfile(userId, {
              isEmailverified: true,
            });
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        // Also update onboarding user verification status
        try {
          await kycService.updateOnboardingUserVerificationByContact(
            personalInfo.email,
            "email",
            true
          );
          // Trigger dashboard refresh to show updated verification status
          dispatch(triggerDashboardRefresh());
        } catch (onboardingUpdateError) {
          console.error(
            "Failed to update onboarding user verification status:",
            onboardingUpdateError
          );
          // Don't block the flow if onboarding user update fails
        }

        toast.success("Email verified successfully");
        // Generate mobile OTP after email is verified
        await generateMobileOTP();
      }
    } catch (error) {
      console.error("Error verifying email OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Invalid OTP. Please try again."
      );
    } finally {
      setIsVerifyingEmail(false);
    }
  };

  // Handle mobile verification
  const handleVerifyMobile = async () => {
    if (mobileOTP.length !== 6) {
      toast.error("Please enter a valid OTP");
      return;
    }

    setIsVerifyingMobile(true);
    try {
      const isValid = await kycService.verifyKYCPhoneOtp(
        personalInfo.mobile,
        mobileOTP
      );

      if (isValid) {
        setIsMobileVerified(true);
        dispatch(verifyMobile(true));

        // Update user's phone verification status
        if (user?.user_id || user?.id) {
          try {
            const userId = user.user_id || user.id!;
            const updatedUser = await authService.updateProfile(userId, {
              isphoneverified: true,
            });
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        // Also update onboarding user verification status
        try {
          await kycService.updateOnboardingUserVerificationByContact(
            personalInfo.mobile,
            "mobile",
            true
          );
          // Trigger dashboard refresh to show updated verification status
          dispatch(triggerDashboardRefresh());
        } catch (onboardingUpdateError) {
          console.error(
            "Failed to update onboarding user verification status:",
            onboardingUpdateError
          );
          // Don't block the flow if onboarding user update fails
        }

        toast.success("Mobile number verified successfully");
      }
    } catch (error) {
      console.error("Error verifying mobile OTP:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Invalid OTP. Please try again."
      );
    } finally {
      setIsVerifyingMobile(false);
    }
  };

  // Handle resend email OTP
  const handleResendEmailOTP = async () => {
    setIsResendingEmail(true);
    try {
      await generateEmailOTP();
    } finally {
      setIsResendingEmail(false);
    }
  };

  // Handle resend mobile OTP
  const handleResendMobileOTP = async () => {
    setIsResendingMobile(true);
    try {
      await generateMobileOTP();
    } finally {
      setIsResendingMobile(false);
    }
  };

  // Handle change email
  const handleChangeEmail = () => {
    navigate("/kyc/verification-process");
  };

  // Handle change mobile
  const handleChangeMobile = () => {
    navigate("/kyc/verification-process");
  };

  // Handle back button
  const handleBack = () => {
    navigate("/kyc/verification-process");
  };

  // Handle proceed to next step
  const handleProceed = () => {
    // Only proceed if both email and mobile are verified
    if (!isEmailVerified || !isMobileVerified) {
      toast.error("Please complete both email and mobile verification first.");
      return;
    }

    // Create new KYC application only after successful verification
    if (!currentApplicationId) {
      // Check if there's pending client data from a modal form
      const pendingClientData = sessionStorage.getItem("pendingClientData");
      let clientData = {
        email: personalInfo.email,
        phone: personalInfo.mobile,
        clientName: "New Client", // Default name
      };

      if (pendingClientData) {
        try {
          const parsedData = JSON.parse(pendingClientData);
          clientData = {
            email: parsedData.email || personalInfo.email,
            phone: parsedData.phone || personalInfo.mobile,
            clientName: parsedData.clientName || "New Client",
          };
          // Clear the pending data after use
          sessionStorage.removeItem("pendingClientData");
        } catch (error) {
          console.error("Error parsing pending client data:", error);
        }
      }

      dispatch(createNewApplication(clientData));
    } else {
      // Save the current application with verified contact information
      dispatch(saveCurrentApplication());
    }

    dispatch(setCurrentStep(2)); // Move to step 2 (Personal Information)
    toast.success(
      "Contact verification completed! Proceeding to personal information."
    );

    // Navigate to personal information form to continue the KYC sequence
    navigate("/kyc/personal");
  };

  return (
    <KYCLayout
      title="Verify Your Contact Info"
      subtitle="Complete the verification process"
    >
      <div className="space-y-8 max-w-md mx-auto">
        {/* Email verification section */}
        <div className="space-y-4">
          <h3 className="text-xl font-medium text-center mb-2">Email OTP</h3>
          <div className="flex justify-center items-center space-x-2 text-sm">
            <span className="text-gray-600">Want to change email?</span>
            <button
              onClick={handleChangeEmail}
              className="text-purple hover:underline font-medium"
            >
              Change email address
            </button>
          </div>

          {!isEmailVerified ? (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Enter the OTP sent to {personalInfo.email}
                </p>
              </div>

              <div className="flex justify-center">
                <OTPInput length={6} onComplete={(code) => setEmailOTP(code)} />
              </div>

              <div className="flex justify-center items-center space-x-2 text-sm">
                <span className="text-gray-600">Didn't receive code?</span>
                <button
                  onClick={handleResendEmailOTP}
                  disabled={isResendingEmail}
                  className="text-purple hover:underline font-medium disabled:text-gray-400"
                >
                  {isResendingEmail ? "Sending..." : "Resend"}
                </button>
              </div>

              <div className="flex justify-center">
                <Button
                  onClick={handleVerifyEmail}
                  disabled={emailOTP.length !== 6 || isVerifyingEmail}
                  className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2"
                >
                  {isVerifyingEmail ? "Verifying..." : "Verify Email"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center text-green-600">
              <CheckCircle className="mr-2" size={18} />
              <span>Email verified successfully</span>
            </div>
          )}
        </div>

        {/* Mobile verification section - only shown after email is verified */}
        {isEmailVerified && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <h3 className="text-xl font-medium text-center mb-2">Mobile OTP</h3>
            <div className="flex justify-center items-center space-x-2 text-sm">
              <span className="text-gray-600">
                Want to change mobile number?
              </span>
              <button
                onClick={handleChangeMobile}
                className="text-purple hover:underline font-medium"
              >
                Change mobile number
              </button>
            </div>

            {!isMobileVerified ? (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Enter the OTP sent to {personalInfo.mobile}
                  </p>
                </div>

                <div className="flex justify-center">
                  <OTPInput
                    length={6}
                    onComplete={(code) => setMobileOTP(code)}
                  />
                </div>

                <div className="flex justify-center items-center space-x-2 text-sm">
                  <span className="text-gray-600">Didn't receive code?</span>
                  <button
                    onClick={handleResendMobileOTP}
                    disabled={isResendingMobile}
                    className="text-purple hover:underline font-medium disabled:text-gray-400"
                  >
                    {isResendingMobile ? "Sending..." : "Resend"}
                  </button>
                </div>

                <div className="flex justify-center">
                  <Button
                    onClick={handleVerifyMobile}
                    disabled={mobileOTP.length !== 6 || isVerifyingMobile}
                    className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2"
                  >
                    {isVerifyingMobile ? "Verifying..." : "Verify Mobile"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center text-green-600">
                <CheckCircle className="mr-2" size={18} />
                <span>Mobile number verified successfully</span>
              </div>
            )}
          </div>
        )}

        {/* Security notice */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
          <div className="flex items-start">
            <CheckCircle className="text-blue-600 mr-2 mt-0.5" size={18} />
            <p className="text-sm text-blue-800">
              Your information is secure and will only be used for verification
              purposes. The process typically takes 5-10 minutes to complete.
            </p>
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            onClick={handleProceed}
            disabled={!isEmailVerified || !isMobileVerified}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            Proceed
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default ContactVerification;
