import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setCurrentStep,
  selectPersonalInfo,
  selectAddressDetails,
  selectAdditionalInfo,
  selectVerificationStatus,
  updatePersonalInfo,
  updateAddressDetails,
  updateAdditionalInfo,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";

// Import refactored components
import PersonalInfoSection from "@/components/kyc/review/PersonalInfoSection";
import AddressSection from "@/components/kyc/review/AddressSection";
import AdditionalInfoSection from "@/components/kyc/review/AdditionalInfoSection";
import DocumentsSection from "@/components/kyc/review/DocumentsSection";

const Review = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const storedPersonalInfo = useAppSelector(selectPersonalInfo);
  const storedAddressDetails = useAppSelector(selectAddressDetails);
  const storedAdditionalInfo = useAppSelector(selectAdditionalInfo);
  const verificationStatus = useAppSelector(selectVerificationStatus);

  // State for toggling edit sections
  const [editingPersonal, setEditingPersonal] = useState(false);
  const [editingAddress, setEditingAddress] = useState(false);
  const [editingAdditional, setEditingAdditional] = useState(false);

  // Form states
  const [personalInfo, setPersonalInfo] = useState(storedPersonalInfo);
  const [addressDetails, setAddressDetails] = useState(storedAddressDetails);
  const [additionalInfo, setAdditionalInfo] = useState(storedAdditionalInfo);

  useEffect(() => {
    dispatch(setCurrentStep(9)); // Step 9: Review
  }, [dispatch]);

  const handlePersonalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPersonalInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handlePersonalSelectChange = (name: string, value: string) => {
    setPersonalInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handlePersonalCheckboxChange = (name: string, checked: boolean) => {
    setPersonalInfo((prev) => ({ ...prev, [name]: checked }));
  };

  const handlePersonalNumberChange = (name: string, value: number) => {
    setPersonalInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddressChange = (
    addressType: "temporary" | "permanent",
    field: string,
    value: string
  ) => {
    setAddressDetails((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        [field]: value,
      },
    }));
  };

  const handleAdditionalChange = (field: string, value: string) => {
    setAdditionalInfo((prev) => ({ ...prev, [field]: value }));
  };

  const handleAdditionalCheckboxChange = (field: string, checked: boolean) => {
    setAdditionalInfo((prev) => ({ ...prev, [field]: checked }));
  };

  const savePersonalInfo = () => {
    dispatch(updatePersonalInfo(personalInfo));
    setEditingPersonal(false);
    toast.success("Personal information updated successfully");
  };

  const saveAddressDetails = () => {
    dispatch(updateAddressDetails(addressDetails));
    setEditingAddress(false);
    toast.success("Address details updated successfully");
  };

  const saveAdditionalInfo = () => {
    dispatch(updateAdditionalInfo(additionalInfo));
    setEditingAdditional(false);
    toast.success("Additional information updated successfully");
  };

  const handleBack = () => {
    navigate("/kyc/summary");
  };

  const handleSubmit = () => {
    toast.success("KYC information submitted successfully");
    navigate("/kyc/completion");
  };

  const handleEditDocuments = () => {
    navigate("/kyc/documents");
  };

  return (
    <KYCLayout
      title="Review & Confirm"
      subtitle="Make sure all details are accurate before submission."
    >
      <div className="space-y-8">
        {/* Personal Information Section */}
        <PersonalInfoSection
          personalInfo={personalInfo}
          editingPersonal={editingPersonal}
          setEditingPersonal={setEditingPersonal}
          handlePersonalChange={handlePersonalChange}
          handleSelectChange={handlePersonalSelectChange}
          handleCheckboxChange={handlePersonalCheckboxChange}
          handleNumberChange={handlePersonalNumberChange}
          savePersonalInfo={savePersonalInfo}
        />

        {/* Address Details Section */}
        <AddressSection
          addressDetails={addressDetails}
          editingAddress={editingAddress}
          setEditingAddress={setEditingAddress}
          handleAddressChange={handleAddressChange}
          saveAddressDetails={saveAddressDetails}
        />

        {/* Additional Information Section */}
        <AdditionalInfoSection
          additionalInfo={additionalInfo}
          editingAdditional={editingAdditional}
          setEditingAdditional={setEditingAdditional}
          handleAdditionalChange={handleAdditionalChange}
          handleAdditionalCheckboxChange={handleAdditionalCheckboxChange}
          saveAdditionalInfo={saveAdditionalInfo}
        />

        {/* Identity Documents Section */}
        <DocumentsSection
          verificationStatus={verificationStatus}
          handleEditDocuments={handleEditDocuments}
        />

        <div className="flex justify-center">
          <Button
            onClick={handleSubmit}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-12 py-2 h-12 rounded-md"
          >
            Submit
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default Review;
