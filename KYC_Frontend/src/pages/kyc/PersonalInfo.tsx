import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  updatePersonalInfo,
  selectPersonalInfo,
  setCurrentStep,
  saveCurrentApplication,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import PersonalInfoForm from "@/components/kyc/PersonalInfoForm";
import { kycService } from "@/services/kycService";

const PersonalInfo = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const storedPersonalInfo = useAppSelector(selectPersonalInfo);

  const [form, setForm] = useState({
    firstName: storedPersonalInfo.firstName || "",
    lastName: storedPersonalInfo.lastName || "",
    email: storedPersonalInfo.email || "",
    mobile: storedPersonalInfo.mobile || "",
    dob: storedPersonalInfo.dob || "",
    gender: storedPersonalInfo.gender || "",
    fatherName: storedPersonalInfo.fatherName || "",
    motherName: storedPersonalInfo.motherName || "",
    isMarried: storedPersonalInfo.isMarried || false,
    spouseName: storedPersonalInfo.spouseName || "",
    dependents: storedPersonalInfo.dependents || 0,
    guardianName: storedPersonalInfo.guardianName || "",
    guardianRelation: storedPersonalInfo.guardianRelation || "",
  });

  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    mobile: "",
    dob: "",
    gender: "",
    guardianName: "",
    guardianRelation: "",
    isMarried: "",
  });

  useEffect(() => {
    dispatch(setCurrentStep(2)); // Step 2: Personal Information
  }, [dispatch]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      firstName: "",
      lastName: "",
      email: "",
      mobile: "",
      dob: "",
      gender: "",
      fatherName: "",
      motherName: "",
      spouseName: "",
      dependents: "",
      guardianName: "",
      guardianRelation: "",
      isMarried: "",
    };

    if (!form.firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    }

    if (!form.lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    }

    if (!form.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }

    if (!form.mobile.trim()) {
      newErrors.mobile = "Mobile number is required";
      isValid = false;
    } else if (!/^\d{10}$/.test(form.mobile)) {
      newErrors.mobile = "Mobile number must be 10 digits";
      isValid = false;
    }

    if (!form.dob.trim()) {
      newErrors.dob = "Date of birth is required";
      isValid = false;
    } else if (!/^\d{2}\/\d{2}\/\d{4}$/.test(form.dob)) {
      newErrors.dob = "Date format should be DD/MM/YYYY";
      isValid = false;
    }

    if (!form.gender) {
      newErrors.gender = "Gender is required";
      isValid = false;
    }

    if (!form.guardianName.trim()) {
      newErrors.guardianName = "Guardian name is required";
      isValid = false;
    }

    if (!form.guardianRelation) {
      newErrors.guardianRelation = "Guardian relation is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setForm((prev) => ({ ...prev, [name]: checked }));
  };

  const handleNumberChange = (name: string, value: number) => {
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Save to Redux only, do not call API here
    dispatch(updatePersonalInfo(form));
    dispatch(setCurrentStep(3));
    dispatch(saveCurrentApplication());
    navigate("/kyc/additional");
  };

  const handleBack = () => {
    navigate("/kyc/verify-mobile");
  };

  return (
    <KYCLayout
      title="Personal Information"
      subtitle="Let's start with your basic identity details."
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <PersonalInfoForm
          formData={form}
          errors={errors}
          handleChange={handleChange}
          handleSelectChange={handleSelectChange}
          handleCheckboxChange={handleCheckboxChange}
          handleNumberChange={handleNumberChange}
        />

        <div className="flex justify-between pt-4">
          <Button
            type="button"
            variant="outline"
            className="border-[#26355E] text-[#26355E] hover:bg-[#26355E] hover:text-white"
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2"
          >
            Next
          </Button>
        </div>
      </form>
    </KYCLayout>
  );
};

export default PersonalInfo;
