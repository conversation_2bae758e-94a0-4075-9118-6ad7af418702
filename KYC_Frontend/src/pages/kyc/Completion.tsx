import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import { completeKYC, setCurrentStep } from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { BadgeCheck } from "lucide-react";

const Completion = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(setCurrentStep(10)); // Step 10: Completion
    dispatch(completeKYC());
  }, [dispatch]);

  const handleBackToDashboard = () => {
    navigate("/dashboard");
  };

  return (
    <KYCLayout
      title="KYC Completed Successfully"
      subtitle="Your details have been submitted and verified. You now have full access to all features."
      showCloseButton={false}
    >
      <div className="flex flex-col items-center py-12">
        <div className="bg-green-500 rounded-full p-2 mb-8">
          <BadgeCheck size={80} className="text-white " />
        </div>

        <Button
          onClick={handleBackToDashboard}
          className="bg-[#26355E] text-white hover:bg-primary-navy/90 text-lg px-12 py-2 h-12 rounded-md mt-12 font-medium"
        >
          Back to dashboard
        </Button>
      </div>
    </KYCLayout>
  );
};

export default Completion;
