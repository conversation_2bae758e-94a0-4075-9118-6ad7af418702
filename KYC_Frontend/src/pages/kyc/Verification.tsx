import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  verifyDocument,
  setCurrentStep,
  selectVerificationStatus,
  saveCurrentApplication,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";

const Verification = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const verificationStatus = useAppSelector(selectVerificationStatus);

  const [isVerifying, setIsVerifying] = useState<Record<string, boolean>>({});
  const [facialImage, setFacialImage] = useState<string | null>(null);

  useEffect(() => {
    dispatch(setCurrentStep(7)); // Step 7: Verification

    // Retrieve facial image from localStorage
    const storedFacialImage = localStorage.getItem("kyc_facial_image");
    if (storedFacialImage) {
      setFacialImage(storedFacialImage);
    }
  }, [dispatch]);

  const handleVerifyDocument = (documentId: string) => {
    setIsVerifying((prev) => ({ ...prev, [documentId]: true }));

    // Simulate verification process
    setTimeout(() => {
      dispatch(verifyDocument({ id: documentId, verified: true }));
      setIsVerifying((prev) => ({ ...prev, [documentId]: false }));
      toast.success("Document verified successfully");
    }, 1500);
  };

  const handleVerifyFace = () => {
    setIsVerifying((prev) => ({ ...prev, face: true }));

    // Simulate facial verification process
    setTimeout(() => {
      dispatch(verifyDocument({ id: "facial", verified: true }));
      setIsVerifying((prev) => ({ ...prev, face: false }));
      toast.success("Face verified successfully");
    }, 1500);
  };

  const handleBack = () => {
    navigate("/kyc/documents");
  };

  const handleNext = () => {
    // Check if all documents are verified
    const allDocsVerified = Object.values(verificationStatus.documents).every(
      (doc) => doc.verified
    );

    if (allDocsVerified) {
      dispatch(setCurrentStep(8)); // Move to step 8 (Summary)
      dispatch(saveCurrentApplication()); // Save progress
      navigate("/kyc/summary");
    } else {
      toast.error("Please verify all documents");
    }
  };

  const documents = Object.entries(verificationStatus.documents);

  return (
    <KYCLayout
      title="Verification"
      subtitle="Verify your documents and image for successful KYC"
    >
      <div className="space-y-8">
        {documents.map(([id, doc]) => (
          <div
            key={id}
            className="flex items-center justify-between py-4 border-b border-gray-100"
          >
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#AF47D2"
                strokeWidth="2"
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
              </svg>
              <span className="ml-4">{doc.name}</span>
            </div>

            {doc.verified ? (
              <div className="py-3 text-center">
                <span className="px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
                  Verified
                </span>
              </div>
            ) : (
              <Button
                onClick={() => handleVerifyDocument(id)}
                disabled={isVerifying[id]}
                className={`${
                  isVerifying[id]
                    ? "bg-gray-300"
                    : "bg-primary-navy hover:bg-primary-navy/90"
                } text-white px-8 py-2 h-12 rounded-md`}
              >
                {isVerifying[id] ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Verifying...</span>
                  </div>
                ) : (
                  "Verify"
                )}
              </Button>
            )}
          </div>
        ))}

        <div className="flex items-center justify-between py-4 border-b border-gray-100">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full overflow-hidden flex items-center justify-center">
              {facialImage ? (
                <img
                  src={facialImage}
                  alt="Facial verification"
                  className="w-full h-full object-cover"
                />
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#666"
                  strokeWidth="2"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              )}
            </div>
            <span className="ml-4">Scanned Photo</span>
          </div>

          {verificationStatus.facial ? (
            <div className="py-3 text-center">
              <span className="px-3 py-2 bg-[#52C41A1A] border-2 border-[#52C41A] text-[#52C41A] rounded-sm text-sm">
                Verified
              </span>
            </div>
          ) : (
            <Button
              onClick={handleVerifyFace}
              disabled={isVerifying.face}
              className={`${
                isVerifying.face
                  ? "bg-gray-300"
                  : "bg-primary-navy hover:bg-primary-navy/90"
              } text-white px-8 py-2 h-12 rounded-md`}
            >
              {isVerifying.face ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Verifying...</span>
                </div>
              ) : (
                "Verify"
              )}
            </Button>
          )}
        </div>

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            onClick={handleNext}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md"
          >
            Next
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default Verification;
