import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  updateAddressDetails,
  selectAddressDetails,
  setCurrentStep,
  selectCurrentApplicationId,
  setApiLoading,
  setApiError,
  selectApiLoading,
  selectApiErrors,
  saveCurrentApplication,
  setAddressSubmissionSuccess,
} from "@/redux/slices/kycSlice";
import { kycService } from "@/services/kycService";
import { authService } from "@/services/authService";
import { useNetworkStatus, getErrorMessage } from "@/hooks/useNetworkStatus";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import AddressForm from "@/components/kyc/AddressForm";

const AddressDetails = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const storedAddressDetails = useAppSelector(selectAddressDetails);
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);
  const apiLoading = useAppSelector(selectApiLoading);
  const apiErrors = useAppSelector(selectApiErrors);
  const { isOnline } = useNetworkStatus();

  const [form, setForm] = useState({
    temporary: {
      street: storedAddressDetails.temporary.street || "",
      city: storedAddressDetails.temporary.city || "",
      state: storedAddressDetails.temporary.state || "",
      pincode: storedAddressDetails.temporary.pincode || "",
    },
    permanent: {
      street: storedAddressDetails.permanent.street || "",
      city: storedAddressDetails.permanent.city || "",
      state: storedAddressDetails.permanent.state || "",
      pincode: storedAddressDetails.permanent.pincode || "",
    },
  });

  const [errors, setErrors] = useState({
    temporary: {
      street: "",
      city: "",
      state: "",
      pincode: "",
    },
    permanent: {
      street: "",
      city: "",
      state: "",
      pincode: "",
    },
  });

  useEffect(() => {
    dispatch(setCurrentStep(4)); // Address Details is now step 4
  }, [dispatch]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      temporary: {
        street: "",
        city: "",
        state: "",
        pincode: "",
      },
      permanent: {
        street: "",
        city: "",
        state: "",
        pincode: "",
      },
    };

    // Validate temporary address
    if (!form.temporary.street.trim()) {
      newErrors.temporary.street = "Street address is required";
      isValid = false;
    }

    if (!form.temporary.city.trim()) {
      newErrors.temporary.city = "City is required";
      isValid = false;
    }

    if (!form.temporary.state.trim()) {
      newErrors.temporary.state = "State is required";
      isValid = false;
    }

    if (!form.temporary.pincode.trim()) {
      newErrors.temporary.pincode = "Pincode is required";
      isValid = false;
    } else if (!/^\d{6}$/.test(form.temporary.pincode)) {
      newErrors.temporary.pincode = "Pincode must be 6 digits";
      isValid = false;
    }

    // Validate permanent address
    if (!form.permanent.street.trim()) {
      newErrors.permanent.street = "Street address is required";
      isValid = false;
    }

    if (!form.permanent.city.trim()) {
      newErrors.permanent.city = "City is required";
      isValid = false;
    }

    if (!form.permanent.state.trim()) {
      newErrors.permanent.state = "State is required";
      isValid = false;
    }

    if (!form.permanent.pincode.trim()) {
      newErrors.permanent.pincode = "Pincode is required";
      isValid = false;
    } else if (!/^\d{6}$/.test(form.permanent.pincode)) {
      newErrors.permanent.pincode = "Pincode must be 6 digits";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (
    addressType: "temporary" | "permanent",
    field: string,
    value: string
  ) => {
    setForm((prev) => ({
      ...prev,
      [addressType]: {
        ...prev[addressType],
        [field]: value,
      },
    }));
  };

  const handleBack = () => {
    navigate("/kyc/additional");
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      // Start API loading state
      dispatch(setApiLoading({ type: "addressInfo", loading: true }));
      dispatch(setApiError({ type: "addressInfo", error: "" }));

      // Update address details in Redux first
      dispatch(updateAddressDetails(form));

      // Check if user is authenticated before making API call
      const tokens = authService.getTokens();
      if (!tokens.accessToken) {
        throw new Error("Please log in to continue with your KYC application");
      }

      // Call API to submit address information
      const response = await kycService.submitAddressInformation(form);

      // Handle successful API response
      if (response.success) {
        // Store API response data
        if (currentApplicationId && response.id) {
          dispatch(
            setAddressSubmissionSuccess({
              applicationId: currentApplicationId,
              apiId: response.id,
              apiData: response.data,
            })
          );
        }

        // Save to current application with API ID
        dispatch(saveCurrentApplication());

        // Update current step to facial verification
        dispatch(setCurrentStep(5)); // Move to step 5 (Facial Verification)

        toast.success("Address details saved successfully");

        // Navigate to facial verification
        navigate("/kyc/facial");
      } else {
        throw new Error(response.message || "Failed to save address details");
      }
    } catch (error) {
      // Handle API errors with network awareness and authentication
      let errorMessage = "Failed to save address details";

      if (error instanceof Error) {
        if (
          error.message.includes("Authentication") ||
          error.message.includes("401")
        ) {
          errorMessage = "Please log in to continue with your KYC application";
          // Optionally redirect to login
          // navigate("/login");
        } else if (error.message.includes("500")) {
          errorMessage =
            "Server error. Please try again later or contact support.";
        } else {
          errorMessage = getErrorMessage(error, isOnline);
        }
      }

      dispatch(setApiError({ type: "addressInfo", error: errorMessage }));
      toast.error(errorMessage);
    } finally {
      // Stop loading state
      dispatch(setApiLoading({ type: "addressInfo", loading: false }));
    }
  };

  return (
    <KYCLayout
      title="Address Details"
      subtitle="Tell us where you currently live and your permanent address."
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        <AddressForm
          addressType="temporary"
          addressData={form.temporary}
          errors={errors.temporary}
          handleChange={handleChange}
          title="Temporary Address"
        />

        <AddressForm
          addressType="permanent"
          addressData={form.permanent}
          errors={errors.permanent}
          handleChange={handleChange}
          title="Permanent Address"
        />

        {apiErrors.addressInfo && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4">
            <div className="flex items-center justify-between">
              <p className="text-red-600 text-sm">{apiErrors.addressInfo}</p>
              <Button
                type="button"
                onClick={() => {
                  dispatch(setApiError({ type: "addressInfo", error: "" }));
                }}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Dismiss
              </Button>
            </div>
          </div>
        )}

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            type="submit"
            disabled={apiLoading.addressInfo || !isOnline}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md disabled:opacity-50"
          >
            {!isOnline
              ? "No Connection"
              : apiLoading.addressInfo
              ? "Saving..."
              : "Proceed"}
          </Button>
        </div>
      </form>
    </KYCLayout>
  );
};

export default AddressDetails;
