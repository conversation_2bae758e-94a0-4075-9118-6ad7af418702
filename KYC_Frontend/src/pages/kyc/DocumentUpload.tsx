import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  addDocument,
  setCurrentStep,
  saveCurrentApplication,
  setApiLoading,
  setApiError,
  selectApiLoading,
  selectApiErrors,
  selectCurrentApplicationId,
  setDocumentSubmissionSuccess,
} from "@/redux/slices/kycSlice";
import { kycService } from "@/services/kycService";
import { authService } from "@/services/authService";
import { useNetworkStatus, getErrorMessage } from "@/hooks/useNetworkStatus";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import DocumentUploadItem from "@/components/kyc/DocumentUploadItem";

interface Document {
  id: string;
  type: string;
  file: File | null;
  preview: string | null;
  isUploaded: boolean;
  isUploading: boolean;
  uploadFailed?: boolean;
  errorMessage?: string;
}

const DocumentUpload = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const apiLoading = useAppSelector(selectApiLoading);
  const apiErrors = useAppSelector(selectApiErrors);
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);
  const { isOnline } = useNetworkStatus();

  const [documents, setDocuments] = useState<Document[]>([
    {
      id: "1",
      type: "",
      file: null,
      preview: null,
      isUploaded: false,
      isUploading: false,
      uploadFailed: false,
      errorMessage: "",
    },
    {
      id: "2",
      type: "",
      file: null,
      preview: null,
      isUploaded: false,
      isUploading: false,
      uploadFailed: false,
      errorMessage: "",
    },
  ]);

  const documentTypes = [
    "Aadhar Card",
    "PAN Card",
    "Passport",
    "Driving License",
    "Voter ID",
    "Bank Statement",
  ];

  useEffect(() => {
    dispatch(setCurrentStep(6)); // Step 6: Document Upload
  }, [dispatch]);

  const handleDocumentTypeChange = (index: number, value: string) => {
    setDocuments((prevDocs) => {
      const newDocs = [...prevDocs];
      newDocs[index] = {
        ...newDocs[index],
        type: value,
      };
      return newDocs;
    });
  };

  const handleFileSelect = (index: number) => {
    const doc = documents[index];
    if (!doc.type) {
      toast.error("Please select document type first");
      return;
    }
    fileInputRef.current?.click();
    fileInputRef.current?.setAttribute("data-index", index.toString());
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    const indexAttr = e.target.getAttribute("data-index");
    const index = indexAttr ? parseInt(indexAttr) : 0;

    if (files && files[0]) {
      const file = files[0];

      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast.error("File size exceeds 2MB limit");
        return;
      }

      // Check file type
      const allowedTypes = ["image/jpeg", "image/png", "application/pdf"];
      if (!allowedTypes.includes(file.type)) {
        toast.error("Only JPG, PNG, and PDF files are allowed");
        return;
      }

      // Create preview for image files
      let preview = null;
      if (file.type.startsWith("image/")) {
        preview = URL.createObjectURL(file);
      } else {
        // For PDFs, use a generic icon
        preview = "pdf";
      }

      setDocuments((prevDocs) => {
        const newDocs = [...prevDocs];
        newDocs[index] = {
          ...newDocs[index],
          file,
          preview,
          isUploading: true,
        };
        return newDocs;
      });

      // Upload document to API
      uploadDocumentToAPI(file, documents[index].type, index);
    }
  };

  const uploadDocumentToAPI = async (
    file: File,
    documentType: string,
    index: number
  ) => {
    try {
      // Start API loading state
      dispatch(setApiLoading({ type: "documentInfo", loading: true }));
      dispatch(setApiError({ type: "documentInfo", error: "" }));

      // Check if user is authenticated before making API call
      const tokens = authService.getTokens();
      if (!tokens.accessToken) {
        throw new Error("Please log in to continue with your KYC application");
      }

      // Create document data for API
      const documentData = {
        document_type: documentType,
        document_file: file,
        is_verified: false, // Initially not verified
      };

      console.log("Uploading document:", {
        type: documentType,
        fileName: file.name,
        fileSize: file.size,
      });

      // Call API to submit document information
      const response = await kycService.submitDocumentInformation(documentData);

      // Handle successful API response
      if (response.success) {
        const documentId = response.id || `doc-${index + 1}`;

        // Store API response data
        if (currentApplicationId && response.id) {
          dispatch(
            setDocumentSubmissionSuccess({
              applicationId: currentApplicationId,
              apiId: response.id,
              apiData: {
                ...((typeof response.data === "object" && response.data !== null) ? response.data : {}),
                document_type: documentType,
                document_number: typeof response.data === "object" && response.data !== null && "document_number" in response.data
                  ? String((response.data as { document_number?: unknown }).document_number)
                  : "",
              },
              documentType: documentType,
            })
          );
        }

        // Read file as base64 for Redux storage (for preview purposes)
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result as string;

          dispatch(
            addDocument({
              id: documentId,
              file: base64String,
              type: documentType,
              name: file.name,
              verified: false,
            })
          );

          setDocuments((prevDocs) => {
            const newDocs = [...prevDocs];
            newDocs[index] = {
              ...newDocs[index],
              isUploaded: true,
              isUploading: false,
            };
            return newDocs;
          });

          toast.success(`${documentType} uploaded successfully`);
        };
        reader.readAsDataURL(file);
      } else {
        throw new Error(response.message || "Failed to upload document");
      }
    } catch (error) {
      // Handle API errors with network awareness and authentication
      let errorMessage = "Failed to upload document";

      if (error instanceof Error) {
        if (
          error.message.includes("Authentication") ||
          error.message.includes("401")
        ) {
          errorMessage = "Please log in to continue with your KYC application";
          // Optionally redirect to login
          // navigate("/login");
        } else if (error.message.includes("500")) {
          errorMessage =
            "Server error. Please try again later or contact support.";
        } else if (error.message.includes("413")) {
          errorMessage =
            "File size too large. Please upload a file smaller than 2MB.";
        } else if (error.message.includes("415")) {
          errorMessage =
            "Unsupported file type. Please upload JPG, PNG, or PDF files only.";
        } else {
          errorMessage = getErrorMessage(error, isOnline);
        }
      }

      dispatch(setApiError({ type: "documentInfo", error: errorMessage }));
      toast.error(errorMessage);

      // Reset upload state on error
      setDocuments((prevDocs) => {
        const newDocs = [...prevDocs];
        newDocs[index] = {
          ...newDocs[index],
          isUploading: false,
          isUploaded: false,
          uploadFailed: true,
          errorMessage: errorMessage,
        };
        return newDocs;
      });
    } finally {
      // Stop loading state
      dispatch(setApiLoading({ type: "documentInfo", loading: false }));
    }
  };

  const handleRetryUpload = (index: number) => {
    const doc = documents[index];
    if (doc.file && doc.type) {
      // Reset error state
      setDocuments((prevDocs) => {
        const newDocs = [...prevDocs];
        newDocs[index] = {
          ...newDocs[index],
          uploadFailed: false,
          errorMessage: "",
          isUploading: true,
        };
        return newDocs;
      });

      // Retry upload
      uploadDocumentToAPI(doc.file, doc.type, index);
    }
  };

  const handleViewDocument = (index: number) => {
    const doc = documents[index];
    if (doc.preview && doc.preview !== "pdf") {
      window.open(doc.preview, "_blank");
    } else if (doc.file && doc.file.type === "application/pdf") {
      const url = URL.createObjectURL(doc.file);
      window.open(url, "_blank");
    }
  };

  const handleBack = () => {
    navigate("/kyc/facial");
  };

  const handleNext = () => {
    // Check if at least one document is uploaded
    const hasUploadedDoc = documents.some((doc) => doc.isUploaded);

    if (!hasUploadedDoc) {
      toast.error("Please upload at least one document");
      return;
    }

    dispatch(setCurrentStep(7)); // Move to step 7 (Verification)
    dispatch(saveCurrentApplication()); // Save progress
    navigate("/kyc/verification");
  };

  return (
    <KYCLayout
      title="Upload Identity Documents"
      subtitle="Upload documents that match the info you provided."
    >
      <div className="space-y-8">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          style={{ display: "none" }}
          accept=".jpg,.jpeg,.png,.pdf"
        />

        {documents.map((doc, index) => (
          <DocumentUploadItem
            key={doc.id}
            doc={doc}
            index={index}
            documentTypes={documentTypes}
            onTypeChange={handleDocumentTypeChange}
            onFileSelect={handleFileSelect}
            onViewDocument={handleViewDocument}
            onRetryUpload={handleRetryUpload}
          />
        ))}

        {apiErrors.documentInfo && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4">
            <div className="flex items-center justify-between">
              <p className="text-red-600 text-sm">{apiErrors.documentInfo}</p>
              <Button
                type="button"
                onClick={() => {
                  dispatch(setApiError({ type: "documentInfo", error: "" }));
                }}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Dismiss
              </Button>
            </div>
          </div>
        )}

        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            onClick={handleNext}
            disabled={apiLoading.documentInfo || !isOnline}
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2 h-12 rounded-md disabled:opacity-50"
          >
            {!isOnline
              ? "No Connection"
              : apiLoading.documentInfo
              ? "Uploading..."
              : "Next"}
          </Button>
        </div>
      </div>
    </KYCLayout>
  );
};

export default DocumentUpload;
