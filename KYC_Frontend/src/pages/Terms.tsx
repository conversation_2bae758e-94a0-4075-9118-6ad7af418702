import React from "react";
import AuthLayout from "@/components/AuthLayout";

const Terms = () => {
  return (
    <AuthLayout>
      <div className="max-w-4xl mx-auto w-full px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Terms of Service</h1>

        <div className="prose prose-purple max-w-none">
          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing and using this platform, you agree to be bound by these
            Terms of Service and all applicable laws and regulations. If you do
            not agree with any of these terms, you are prohibited from using or
            accessing this site.
          </p>

          <h2>2. Use License</h2>
          <p>
            Permission is granted to temporarily access the materials
            (information or software) on our platform for personal,
            non-commercial transitory viewing only.
          </p>

          <h2>3. User Account</h2>
          <p>
            You are responsible for maintaining the confidentiality of your
            account and password. You agree to accept responsibility for all
            activities that occur under your account.
          </p>

          <h2>4. Privacy Policy</h2>
          <p>
            Your use of our platform is also governed by our Privacy Policy.
            Please review our Privacy Policy, which also governs the platform
            and informs users of our data collection practices.
          </p>

          <h2>5. Disclaimer</h2>
          <p>
            The materials on our platform are provided on an 'as is' basis. We
            make no warranties, expressed or implied, and hereby disclaim and
            negate all other warranties including, without limitation, implied
            warranties or conditions of merchantability, fitness for a
            particular purpose, or non-infringement of intellectual property or
            other violation of rights.
          </p>

          <h2>6. Limitations</h2>
          <p>
            In no event shall we or our suppliers be liable for any damages
            (including, without limitation, damages for loss of data or profit,
            or due to business interruption) arising out of the use or inability
            to use the materials on our platform.
          </p>

          <h2>7. Revisions and Errata</h2>
          <p>
            The materials appearing on our platform could include technical,
            typographical, or photographic errors. We do not warrant that any of
            the materials on our platform are accurate, complete, or current.
          </p>

          <h2>8. Links</h2>
          <p>
            We have not reviewed all of the sites linked to our platform and are
            not responsible for the contents of any such linked site. The
            inclusion of any link does not imply endorsement by us of the site.
          </p>

          <h2>9. Modifications</h2>
          <p>
            We may revise these terms of service at any time without notice. By
            using this platform, you are agreeing to be bound by the then
            current version of these terms of service.
          </p>

          <h2>10. Governing Law</h2>
          <p>
            These terms and conditions are governed by and construed in
            accordance with the laws and you irrevocably submit to the exclusive
            jurisdiction of the courts in that location.
          </p>
        </div>
      </div>
    </AuthLayout>
  );
};

export default Terms;
