import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectUser } from "@/redux/slices/authSlice";
import DashboardSidebar from "@/components/dashboard/DashboardSidebar";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/sonner";
import { authService } from "@/services/authService";

const ChangePassword = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);

  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleForgotPassword = () => {
    navigate("/forgot-password");
  };

  const handleSubmit = async () => {
    if (
      !formData.currentPassword ||
      !formData.newPassword ||
      !formData.confirmPassword
    ) {
      toast.error("Please fill all fields");
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      toast.error("New passwords do not match");
      return;
    }

    setIsLoading(true);

    try {
      if (user?.email) {
        // In a real app, we would verify the current password first
        await authService.resetPassword(user.email, formData.newPassword);
        toast.success("Password changed successfully");
        navigate("/password-changed");
      }
    } catch (error) {
      toast.error("Failed to change password");
      console.error("Password change error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex font-urbanist">
      <DashboardSidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader />

        <section className="flex-1 flex justify-center overflow-y-auto p-4 sm:p-6">
          <div className="w-full max-w-4xl">
            <div className="p-4 sm:p-10">
              <h2 className="text-xl sm:text-2xl font-bold mb-2">
                Change password
              </h2>
              <p className="text-gray-600 mb-8">
                Your new password must be unique from those previously used.
              </p>
              <div className="space-y-4 mb-6">
                <Input
                  type="password"
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleInputChange}
                  placeholder="Current Password"
                  className="bg-[#F8F9FB] focus:ring-2 focus:ring-[#AF47D2]"
                />
                <div className="flex flex-col sm:flex-row gap-4">
                  <Input
                    type="password"
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    placeholder="New Password"
                    className="bg-[#F8F9FB] focus:ring-2 focus:ring-[#AF47D2]"
                  />
                  <Input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm New Password"
                    className="bg-[#F8F9FB] focus:ring-2 focus:ring-[#AF47D2]"
                  />
                </div>
              </div>
              <div className="flex justify-center mb-8">
                <button
                  onClick={handleForgotPassword}
                  className="text-[#AF47D2] hover:underline text-sm"
                  type="button"
                >
                  Forgot Password?
                </button>
              </div>
              <div className="flex flex-col items-center mb-8">
                <Button
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="bg-[#26355E] text-white hover:bg-[#1d2747] py-3 text-md font-semibold rounded-lg w-full sm:w-auto"
                >
                  {isLoading ? "Processing..." : "Save changes"}
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ChangePassword;
