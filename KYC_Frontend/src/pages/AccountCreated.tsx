import React from "react";
import { useNavigate } from "react-router-dom";
import AuthLayout from "@/components/AuthLayout";
import AuthButton from "@/components/ui/auth-button";
import { BadgeCheck, Check } from "lucide-react";

const AccountCreated = () => {
  const navigate = useNavigate();

  const handleProceed = () => {
    navigate("/login");
  };

  return (
    <AuthLayout>
      <div className="max-w-lg mx-auto w-full text-center">
        <div className="flex flex-col items-center py-12">
          <div className="bg-green-500 rounded-full p-2 mb-8">
            <BadgeCheck size={80} className="text-white " />
          </div>
        </div>

        <h1 className="text-3xl font-bold mb-2">
          Account Created Successfully
        </h1>
        <p className="text-gray-600 mb-10">
          Your account has been created successfully. Both your email and phone
          number have been verified. You can now log in to access your
          dashboard.
        </p>

        <AuthButton onClick={handleProceed} className="w-full">
          Continue to Login
        </AuthButton>
      </div>
    </AuthLayout>
  );
};

export default AccountCreated;
