import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/redux/hooks";
import { selectIsAuthenticated } from "@/redux/slices/authSlice";
import {
  selectIsKYCCompleted,
  selectKYCApplications,
} from "@/redux/slices/kycSlice";

// Import refactored components
import DashboardSidebar from "@/components/dashboard/DashboardSidebar";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import ProfilePicturePrompt from "@/components/dashboard/ProfilePicturePrompt";
import UnifiedKycDashboard from "@/components/dashboard/UnifiedKycDashboard";

const Dashboard = () => {
  const navigate = useNavigate();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isKYCCompleted = useAppSelector(selectIsKYCCompleted);
  const applications = useAppSelector(selectKYCApplications);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  return (
    <div className="flex font-urbanist">
      {/* Sidebar */}
      <DashboardSidebar />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader />

        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-full mx-auto">
            {/* Profile Picture Prompt */}
            <ProfilePicturePrompt />

            {/* Unified KYC Dashboard - consolidates all KYC data into a single table */}
            <UnifiedKycDashboard />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
