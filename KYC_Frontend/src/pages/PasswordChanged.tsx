
import React from "react";
import { useNavigate } from "react-router-dom";
import AuthLayout from "@/components/AuthLayout";
import AuthButton from "@/components/ui/auth-button";
import { BadgeCheck } from "lucide-react";

const PasswordChanged = () => {
  const navigate = useNavigate();

  const handleBackToDashboard = () => {
    navigate("/dashboard");
  };

  return (
    <AuthLayout>
      <div className="max-w-lg mx-auto w-full text-center">
        <div className="flex flex-col items-center py-12">
          <div className="bg-green-500 rounded-full p-2 mb-8">
            <BadgeCheck size={80} className="text-white " />
          </div>
        </div>

        <h1 className="text-3xl font-bold mb-2">Password Changed!</h1>
        <p className="text-gray-600 mb-10">
          Your password has been changed successfully.
        </p>

        <AuthButton onClick={handleBackToDashboard}>Back to Dashboard</AuthButton>
      </div>
    </AuthLayout>
  );
};

export default PasswordChanged;
