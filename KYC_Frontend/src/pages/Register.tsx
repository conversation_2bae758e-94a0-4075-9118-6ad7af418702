import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import {
  setRegistrationData,
  setRegistrationStep,
} from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import AuthInput from "@/components/ui/auth-input";
import AuthButton from "@/components/ui/auth-button";
import { toast } from "@/components/ui/sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { authService } from "@/services/authService";
import { kycService } from "@/services/kycService";
import { User } from "../types/user";

const Register = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone_no: "",
    password: "",
    confirmPassword: "",
    gender: "Male", // Default gender
    terms: false,
  });

  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone_no: "",
    password: "",
    confirmPassword: "",
    terms: "",
  });

  // Clear any existing registration data when component mounts
  useEffect(() => {
    dispatch(setRegistrationData(null));
  }, [dispatch]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      firstName: "",
      lastName: "",
      email: "",
      phone_no: "",
      password: "",
      confirmPassword: "",
      terms: "",
    };

    // First Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
      isValid = false;
    } else if (formData.firstName.length < 2) {
      newErrors.firstName = "First name must be at least 2 characters";
      isValid = false;
    }

    // Last Name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
      isValid = false;
    } else if (formData.lastName.length < 2) {
      newErrors.lastName = "Last name must be at least 2 characters";
      isValid = false;
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (
      !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)
    ) {
      newErrors.email = "Invalid email address";
      isValid = false;
    }

    // Phone number validation
    if (!formData.phone_no.trim()) {
      newErrors.phone_no = "Phone number is required";
      isValid = false;
    } else if (!/^\d{10}$/.test(formData.phone_no)) {
      newErrors.phone_no = "Please enter a valid 10-digit phone number";
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
      isValid = false;
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password =
        "Password must contain at least one uppercase letter, one lowercase letter, and one number";
      isValid = false;
    }

    // Confirm Password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
      isValid = false;
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    // Terms validation
    if (!formData.terms) {
      newErrors.terms = "You must accept the terms and conditions";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({
      firstName: "",
      lastName: "",
      email: "",
      phone_no: "",
      password: "",
      confirmPassword: "",
      terms: "",
    });
    setIsLoading(true);

    try {
      if (!validateForm()) return;

      // Create user object
      const userData: User = {
        name: `${formData.firstName} ${formData.lastName}`,
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone_no: formData.phone_no,
        password: formData.password,
        gender: formData.gender,
      };

      // Check for duplicate email and phone in onboarding users
      try {
        const duplicateCheck = await kycService.checkDuplicateContact(
          formData.email,
          formData.phone_no
        );

        if (duplicateCheck.hasConflict) {
          const errors = [];
          if (duplicateCheck.emailExists) {
            errors.push(
              `Email "${formData.email}" is already registered in our system`
            );
          }
          if (duplicateCheck.phoneExists) {
            errors.push(
              `Phone number "${formData.phone_no}" is already registered in our system`
            );
          }

          toast.error(
            errors.join(". ") + ". Please use different credentials."
          );
          return;
        }
      } catch (duplicateError) {
        console.error("Error checking duplicates:", duplicateError);
        // Continue with registration if duplicate check fails
      }

      // Register user first (required for OTP generation in backend)
      const result = await authService.register(userData);

      // Check for success - handle both IsSucess and IsSuccess variations
      if (result.IsSucess || result.IsSuccess) {
        toast.success(
          "Registration successful! Please verify your email and phone."
        );
        // Store registration data for OTP verification
        dispatch(setRegistrationData(userData));
        dispatch(setRegistrationStep("email-otp"));
        // Redirect to OTP verification page
        navigate("/verify-otp-email");
      } else {
        // Check if error is due to existing user
        const errorMessage =
          result.Description || result.message || "Registration failed";

        if (
          errorMessage.toLowerCase().includes("already exists") ||
          errorMessage.toLowerCase().includes("user exist")
        ) {
          // User already exists, check if they want to continue with OTP verification
          const continueWithOTP = window.confirm(
            "An account with this email or phone number already exists. " +
              "Would you like to continue with OTP verification to complete your registration?"
          );

          if (continueWithOTP) {
            // Store registration data and proceed to OTP verification
            dispatch(setRegistrationData(userData));
            dispatch(setRegistrationStep("email-otp"));
            toast.info(
              "Continuing with OTP verification for existing account."
            );
            navigate("/verify-otp-email");
            return;
          } else {
            toast.error(
              "Registration cancelled. Please try with different credentials."
            );
            return;
          }
        }

        console.error("Registration failed with response:", result);
        throw new Error(errorMessage);
      }
    } catch (err) {
      console.error("Registration error:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Registration failed";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-xl mx-auto w-full">
        <h1 className="text-3xl font-bold mb-2 text-center">
          Create your account
        </h1>
        <p className="text-gray-600 mb-4 text-center">
          Join us to get started with your journey.
        </p>
        {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-blue-800 text-sm">
            <strong>Important:</strong> After registration, you'll need to
            verify both your email and phone number with OTP codes. Please
            ensure you have access to both before proceeding.
          </p>
        </div> */}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <AuthInput
              name="firstName"
              placeholder="First name"
              value={formData.firstName}
              onChange={handleInputChange}
              error={errors.firstName}
              disabled={isLoading}
            />

            <AuthInput
              name="lastName"
              placeholder="Last name"
              value={formData.lastName}
              onChange={handleInputChange}
              error={errors.lastName}
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <AuthInput
              name="email"
              type="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleInputChange}
              error={errors.email}
              disabled={isLoading}
            />

            <div>
              <AuthInput
                name="phone_no"
                type="tel"
                required
                placeholder="Phone Number"
                value={formData.phone_no}
                onChange={handleInputChange}
              />
              {errors.phone_no && (
                <p className="text-red-500 text-xs mt-1">{errors.phone_no}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <select
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleInputChange}
              disabled={isLoading}
              // className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
              className="w-full px-4 py-3 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-light"
            >
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <AuthInput
              name="password"
              type="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              disabled={isLoading}
            />

            <AuthInput
              name="confirmPassword"
              type="password"
              placeholder="Confirm Password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              error={errors.confirmPassword}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms"
              checked={formData.terms}
              onCheckedChange={(checked) => {
                setFormData((prev) => ({ ...prev, terms: checked === true }));
                if (errors.terms) {
                  setErrors((prev) => ({ ...prev, terms: "" }));
                }
              }}
              disabled={isLoading}
            />
            <label htmlFor="terms" className="text-sm text-gray-600">
              I agree to the{" "}
              <Link to="/terms" className="text-purple hover:text-purple-dark">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link
                to="/privacy"
                className="text-purple hover:text-purple-dark"
              >
                Privacy Policy
              </Link>
              .
            </label>
          </div>

          {errors.terms && (
            <p className="text-sm text-red-500">{errors.terms}</p>
          )}

          <AuthButton type="submit" isLoading={isLoading} className="w-full">
            Create Account
          </AuthButton>
        </form>

        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link
              to="/login"
              className="text-purple hover:text-purple-dark font-medium"
            >
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
};

export default Register;
