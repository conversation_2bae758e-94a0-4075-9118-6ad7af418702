import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setOtpVerified,
  selectRegistrationData,
  setRegistrationStep,
  resetRegistrationFlow,
  updateUser,
} from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import OTPInput from "@/components/ui/otp-input";
import AuthButton from "@/components/ui/auth-button";
import { authService } from "@/services/authService";
import { toast } from "@/components/ui/sonner";

const VerifyOtpEmail = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const registrationData = useAppSelector(selectRegistrationData);

  const [verificationCode, setVerificationCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (!registrationData?.email) {
      // Redirect to register if no email is available
      navigate("/register");
    } else {
      // Send initial OTP
      sendOtp();
    }
  }, [registrationData, navigate]);

  useEffect(() => {
    // Countdown timer for resend
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const sendOtp = async () => {
    if (!registrationData?.email) return;

    setIsLoading(true);
    try {
      await authService.sendEmailOtp(registrationData.email);
      toast.success(`OTP sent to ${registrationData.email}`);
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = () => {
    if (canResend) {
      sendOtp();
    }
  };

  const handleVerify = async () => {
    if (!registrationData?.email) return;
    if (verificationCode.length !== 6) {
      toast.error("Please enter the complete verification code");
      return;
    }

    setIsLoading(true);

    try {
      const isValid = await authService.verifyEmailOtp(
        registrationData.email,
        verificationCode
      );

      if (isValid) {
        dispatch(setOtpVerified({ type: "email", value: true }));
        dispatch(setRegistrationStep("mobile-otp"));

        // Update user's email verification status
        if (registrationData.user_id || registrationData.id) {
          try {
            const userId = registrationData.user_id || registrationData.id!;
            const updatedUser = await authService.updateProfile(userId, {
              isEmailverified: true,
            });
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        toast.success("Email verification successful");

        // Navigate to mobile OTP verification
        navigate("/verify-otp-mobile");
      } else {
        toast.error("Invalid verification code");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      toast.error("Verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOver = () => {
    const confirmStartOver = window.confirm(
      "Are you sure you want to start over? This will clear your current registration progress."
    );

    if (confirmStartOver) {
      dispatch(resetRegistrationFlow());
      authService.cleanupIncompleteRegistration();
      toast.info("Registration progress cleared. You can start fresh.");
      navigate("/register");
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-xl mx-auto w-full text-center">
        <h1 className="text-3xl font-bold mb-2">OTP Verification</h1>
        <p className="text-gray-600 mb-10">
          Enter the verification code we just sent on your email address.
        </p>

        <OTPInput
          length={6}
          onComplete={setVerificationCode}
          className="mb-8"
        />

        <AuthButton
          onClick={handleVerify}
          isLoading={isLoading}
          className="mb-6"
        >
          Verify
        </AuthButton>

        <div className="text-gray-600 space-y-2">
          <div>
            Didn't received code?{" "}
            <button
              disabled={!canResend || isLoading}
              onClick={handleResend}
              className={`text-purple font-medium ${
                !canResend ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {canResend ? "Resend" : `Resend in ${countdown}s`}
            </button>
          </div>
          <div>
            <button
              onClick={handleStartOver}
              disabled={isLoading}
              className="text-red-500 hover:text-red-700 font-medium text-sm"
            >
              Start Over with New Registration
            </button>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
};

export default VerifyOtpEmail;
