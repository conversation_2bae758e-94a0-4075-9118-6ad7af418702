import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setOtpVerified,
  selectRegistrationData,
  selectOtpVerified,
  setRegistrationStep,
  resetRegistrationFlow,
  updateUser,
} from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import OTPInput from "@/components/ui/otp-input";
import AuthButton from "@/components/ui/auth-button";
import { authService } from "@/services/authService";
import { toast } from "@/components/ui/sonner";

const VerifyOtpMobile = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const registrationData = useAppSelector(selectRegistrationData);
  const otpVerified = useAppSelector(selectOtpVerified);

  const [verificationCode, setVerificationCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (!registrationData?.phone_no) {
      // Redirect to register if no phone is available
      navigate("/register");
    } else if (!otpVerified.email) {
      // Ensure email was verified first
      navigate("/verify-otp-email");
    } else {
      // Send initial OTP
      sendOtp();
    }
  }, [registrationData, otpVerified.email, navigate]);

  useEffect(() => {
    // Countdown timer for resend
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const sendOtp = async () => {
    if (!registrationData?.phone_no) return;

    setIsLoading(true);
    try {
      await authService.sendPhoneOtp(registrationData.phone_no);
      toast.success(`OTP sent to ${registrationData.phone_no}`);
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error("Failed to send OTP:", error);
      toast.error("Failed to send OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = () => {
    if (canResend) {
      sendOtp();
    }
  };

  const handleVerify = async () => {
    if (!registrationData?.phone_no) return;
    if (verificationCode.length !== 6) {
      toast.error("Please enter the complete verification code");
      return;
    }

    setIsLoading(true);

    try {
      const isValid = await authService.verifyPhoneOtp(
        registrationData.phone_no,
        verificationCode
      );

      if (isValid) {
        dispatch(setOtpVerified({ type: "mobile", value: true }));
        dispatch(setRegistrationStep("profile-picture"));

        // Update user's phone verification status
        if (registrationData.user_id || registrationData.id) {
          try {
            const userId = registrationData.user_id || registrationData.id!;
            const updatedUser = await authService.updateProfile(userId, {
              isphoneverified: true,
            });
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        toast.success("Mobile verification successful");

        // Navigate to account created page (profile picture can be updated later)
        navigate("/account-created");
      } else {
        toast.error("Invalid verification code");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      toast.error("Verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOver = () => {
    const confirmStartOver = window.confirm(
      "Are you sure you want to start over? This will clear your current registration progress."
    );

    if (confirmStartOver) {
      dispatch(resetRegistrationFlow());
      authService.cleanupIncompleteRegistration();
      toast.info("Registration progress cleared. You can start fresh.");
      navigate("/register");
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-xl mx-auto w-full text-center">
        <h1 className="text-3xl font-bold mb-2">OTP Verification</h1>
        <p className="text-gray-600 mb-10">
          Enter the verification code we just sent on your mobile number.
        </p>

        <OTPInput
          length={6}
          onComplete={setVerificationCode}
          className="mb-8"
        />

        <AuthButton
          onClick={handleVerify}
          isLoading={isLoading}
          className="mb-6"
        >
          Verify
        </AuthButton>

        <div className="text-gray-600 space-y-2">
          <div>
            Didn't received code?{" "}
            <button
              disabled={!canResend || isLoading}
              onClick={handleResend}
              className={`text-purple font-medium ${
                !canResend ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {canResend ? "Resend" : `Resend in ${countdown}s`}
            </button>
          </div>
          <div>
            <button
              onClick={handleStartOver}
              disabled={isLoading}
              className="text-red-500 hover:text-red-700 font-medium text-sm"
            >
              Start Over with New Registration
            </button>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
};

export default VerifyOtpMobile;
