import React, { useState, useEffect } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import {
  loginStart,
  loginSuccess,
  loginFailure,
} from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import AuthInput from "@/components/ui/auth-input";
import AuthButton from "@/components/ui/auth-button";
import { authService } from "@/services/authService";
import { toast } from "@/components/ui/sonner";
import { Eye, EyeOff } from "lucide-react";

interface LocationState {
  from?: {
    pathname: string;
  };
}

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const from =
    (location.state as LocationState)?.from?.pathname || "/dashboard";

  const [identifier, setIdentifier] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    identifier: "",
    password: "",
  });

  // Clear any existing auth errors when component mounts
  useEffect(() => {
    dispatch(loginFailure(null));
  }, [dispatch]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      identifier: "",
      password: "",
    };

    if (!identifier.trim()) {
      newErrors.identifier = "Email or mobile number is required";
      isValid = false;
    }

    if (!password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    dispatch(loginStart());

    try {
      const loginResponse = await authService.login(identifier, password);

      if (loginResponse.IsSucess && loginResponse.user) {
        dispatch(
          loginSuccess({
            user: loginResponse.user,
            accessToken: loginResponse.access_token,
            refreshToken: loginResponse.refresh_token,
          })
        );
        toast.success("Login successful!");
        navigate(from, { replace: true });
      } else {
        // Custom error message for invalid credentials
        const errorMsg =
          loginResponse?.message ||
          "Wrong input. Please check your email/mobile number and password.";
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error("Login error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";

      // The authService now provides human-readable error messages
      // We can use them directly or enhance them further
      const specificError = errorMessage;
      const fieldErrors = { identifier: "", password: "" };

      // Check if we need to set field-specific errors
      if (
        errorMessage.toLowerCase().includes("email") ||
        errorMessage.toLowerCase().includes("invalid email")
      ) {
        fieldErrors.identifier = "Please check your email address";
      } else if (
        errorMessage.toLowerCase().includes("phone") ||
        errorMessage.toLowerCase().includes("invalid phone")
      ) {
        fieldErrors.identifier = "Please check your phone number";
      } else if (
        errorMessage.toLowerCase().includes("password") ||
        errorMessage.toLowerCase().includes("incorrect password") ||
        errorMessage.toLowerCase().includes("wrong password")
      ) {
        fieldErrors.password = "Please check your password";
      } else if (
        errorMessage.toLowerCase().includes("credentials") ||
        errorMessage.toLowerCase().includes("invalid")
      ) {
        fieldErrors.identifier = "Please verify your email/phone";
        fieldErrors.password = "Please verify your password";
      } else if (errorMessage.toLowerCase().includes("account not found")) {
        fieldErrors.identifier = "Account not found with this email/phone";
      }

      setErrors(fieldErrors);
      dispatch(loginFailure(specificError));
      toast.error(specificError);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginWithOTP = () => {
    navigate("/login-otp");
  };

  return (
    <AuthLayout>
      <div className="max-w-md mx-auto w-full">
        <h1 className="text-3xl font-bold mb-2 text-center">Welcome back!</h1>
        <p className="text-gray-600 mb-8 text-center">
          Please sign in to continue.
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <AuthInput
              type="text"
              placeholder="Email or mobile number"
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
              error={errors.identifier}
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <div className="relative">
              <AuthInput
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                error={errors.password}
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link
              to="/forgot-password"
              className="text-sm text-purple hover:text-purple-dark"
            >
              Forgot password?
            </Link>
          </div>

          <AuthButton type="submit" isLoading={isLoading} className="w-full">
            Sign In
          </AuthButton>
        </form>

        <div className="text-center mt-6">
          <p className="text-gray-600">
            Don't have an account?{" "}
            <Link to="/register" className="text-purple font-medium">
              Register Now
            </Link>
          </p>
        </div>

        <div className="text-center mt-6">
          <p className="text-gray-600">Or</p>
        </div>

        <AuthButton
          type="button"
          variant="outline"
          onClick={handleLoginWithOTP}
          className="w-full"
        >
          Login with OTP
        </AuthButton>
      </div>
    </AuthLayout>
  );
};

export default Login;
