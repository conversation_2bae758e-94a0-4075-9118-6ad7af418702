
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@/redux/hooks';
import { selectForgotPasswordEmail } from '@/redux/slices/authSlice';
import AuthLayout from '@/components/AuthLayout';
import AuthInput from '@/components/ui/auth-input';
import AuthButton from '@/components/ui/auth-button';
import { authService } from '@/services/authService';
import { toast } from '@/components/ui/sonner';

const ResetPassword = () => {
  const navigate = useNavigate();
  const forgotPasswordEmail = useAppSelector(selectForgotPasswordEmail);
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    password: '',
    confirmPassword: '',
  });

  useEffect(() => {
    if (!forgotPasswordEmail) {
      // Redirect if no email is available
      navigate('/forgot-password');
    }
  }, [forgotPasswordEmail, navigate]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      password: '',
      confirmPassword: '',
    };

    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !forgotPasswordEmail) return;
    
    setIsLoading(true);
    
    try {
      await authService.resetPassword(forgotPasswordEmail, password);
      toast.success('Password reset successful');
      navigate('/password-changed');
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-lg mx-auto w-full">
        <h1 className="text-3xl font-bold mb-2">Create new password</h1>
        <p className="text-gray-600 mb-8">
          Your new password must be unique from those previously used.
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <AuthInput
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            error={errors.password}
          />
          
          <AuthInput
            type="password"
            placeholder="Confirm Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            error={errors.confirmPassword}
          />
          
          <AuthButton type="submit" isLoading={isLoading}>
            Reset Password
          </AuthButton>
        </form>
      </div>
    </AuthLayout>
  );
};

export default ResetPassword;
