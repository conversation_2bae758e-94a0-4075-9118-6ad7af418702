import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setOtpVerified,
  selectUser,
  updateUser,
} from "@/redux/slices/authSlice";
import { triggerDashboardRefresh } from "@/redux/slices/kycSlice";
import AuthLayout from "@/components/AuthLayout";
import AuthInput from "@/components/ui/auth-input";
import AuthButton from "@/components/ui/auth-button";
import { toast } from "@/components/ui/sonner";
import { authService } from "@/services/authService";
import { kycService } from "@/services/kycService";

interface LocationState {
  type: "email" | "mobile";
  contact: string;
  redirectTo?: string;
}

const VerifyOTP = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const registrationData = useAppSelector(
    (state) => state.auth.registrationData
  );
  const user = useAppSelector(selectUser);

  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const { type, contact, redirectTo } = (location.state as LocationState) || {};

  useEffect(() => {
    if (!type || !contact) {
      navigate("/login");
      return;
    }

    // Start countdown for resend OTP
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [type, contact, navigate]);

  const handleResendOTP = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      await authService.sendOtp(contact, type);
      setCountdown(60);
      setCanResend(false);
      toast.success(`OTP resent to your ${type}`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to resend OTP";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp.trim()) {
      setError("Please enter the OTP");
      return;
    }

    if (otp.length !== 6) {
      setError("OTP must be 6 digits");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const isValid = await authService.verifyOtp(contact, otp, type);

      if (isValid) {
        dispatch(setOtpVerified({ type, value: true }));

        // Update user's verification status
        if (user?.user_id || user?.id) {
          try {
            const userId = user.user_id || user.id!;
            const verificationUpdate =
              type === "email"
                ? { isEmailverified: true }
                : { isphoneverified: true };
            const updatedUser = await authService.updateProfile(
              userId,
              verificationUpdate
            );
            dispatch(updateUser(updatedUser));
          } catch (updateError) {
            console.error(
              "Failed to update user verification status:",
              updateError
            );
            // Don't block the flow if user update fails
          }
        }

        // Also update onboarding user verification status
        try {
          await kycService.updateOnboardingUserVerificationByContact(
            contact,
            type,
            true
          );
          // Trigger dashboard refresh to show updated verification status
          dispatch(triggerDashboardRefresh());
        } catch (onboardingUpdateError) {
          console.error(
            "Failed to update onboarding user verification status:",
            onboardingUpdateError
          );
          // Don't block the flow if onboarding user update fails
        }

        toast.success(
          `${
            type === "email" ? "Email" : "Mobile number"
          } verified successfully!`
        );

        // If this is part of registration, check if both email and mobile are verified
        if (registrationData) {
          const isEmailVerified = type === "email";
          const isMobileVerified = type === "mobile";

          if (isEmailVerified && isMobileVerified) {
            // Both verified, proceed to dashboard
            navigate("/dashboard");
          } else {
            // Need to verify the other one
            const nextType = isEmailVerified ? "mobile" : "email";
            const nextContact = isEmailVerified
              ? registrationData.phone_no
              : registrationData.email;
            navigate("/verify-otp", {
              state: {
                type: nextType,
                contact: nextContact,
                redirectTo: "/dashboard",
              },
            });
          }
        } else {
          // If not part of registration, redirect to the specified page or dashboard
          // For onboarding users, always redirect back to dashboard to see updated status
          navigate(redirectTo || "/dashboard");
        }
      } else {
        setError("Invalid OTP");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to verify OTP";
      toast.error(errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="max-w-md mx-auto w-full">
        <h1 className="text-3xl font-bold mb-2">Verify your {type}</h1>
        <p className="text-gray-600 mb-8">
          We've sent a 4-digit code to your{" "}
          {type === "email" ? "email" : "mobile number"}
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <AuthInput
              type="text"
              placeholder="Enter 6-digit code"
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, "").slice(0, 6);
                setOtp(value);
                setError("");
              }}
              error={error}
              disabled={isLoading}
              maxLength={6}
              pattern="\d{6}"
              inputMode="numeric"
            />
          </div>

          <AuthButton type="submit" isLoading={isLoading} className="w-full">
            Verify
          </AuthButton>

          <div className="text-center">
            <p className="text-gray-600">
              Didn't receive the code?{" "}
              {canResend ? (
                <button
                  type="button"
                  onClick={handleResendOTP}
                  className="text-purple hover:text-purple-dark font-medium"
                  disabled={isLoading}
                >
                  Resend OTP
                </button>
              ) : (
                <span className="text-gray-500">Resend in {countdown}s</span>
              )}
            </p>
          </div>
        </form>
      </div>
    </AuthLayout>
  );
};

export default VerifyOTP;
