import React, { useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { useAppDispatch } from "@/redux/hooks";
import {
  loginStart,
  loginSuccess,
  loginFailure,
} from "@/redux/slices/authSlice";
import AuthLayout from "@/components/AuthLayout";
import AuthInput from "@/components/ui/auth-input";
import AuthButton from "@/components/ui/auth-button";
import OTPInput from "@/components/ui/otp-input";
import { authService } from "@/services/authService";
import { toast } from "@/components/ui/sonner";

const LoginOTP = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [step, setStep] = useState<"contact" | "otp">("contact");
  const [contact, setContact] = useState("");
  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);
  const [errors, setErrors] = useState({
    contact: "",
    otp: "",
  });

  const validateContact = () => {
    if (!contact.trim()) {
      setErrors({ ...errors, contact: "Email or mobile number is required" });
      return false;
    }

    // Check if it's a valid email or 10-digit phone number
    const isEmail = contact.includes("@");
    const isPhone = /^\d{10}$/.test(contact);

    if (!isEmail && !isPhone) {
      setErrors({
        ...errors,
        contact: "Please enter a valid email or 10-digit mobile number",
      });
      return false;
    }

    setErrors({ ...errors, contact: "" });
    return true;
  };

  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateContact()) return;

    setIsLoading(true);

    try {
      await authService.sendLoginOtp(contact);
      setStep("otp");
      setCountdown(60);
      setCanResend(false);
      toast.success("OTP sent successfully!");

      // Start countdown
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Send OTP error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to send OTP";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      await authService.sendLoginOtp(contact);
      setCountdown(60);
      setCanResend(false);
      toast.success("OTP resent successfully!");

      // Start countdown
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to resend OTP";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!otp || otp.length !== 6) {
      setErrors({ ...errors, otp: "Please enter the complete 6-digit OTP" });
      return;
    }

    setIsLoading(true);
    dispatch(loginStart());

    try {
      const loginResponse = await authService.loginWithOtp(contact, otp);

      if (loginResponse.IsSucess && loginResponse.user) {
        dispatch(
          loginSuccess({
            user: loginResponse.user,
            accessToken: loginResponse.access_token,
            refreshToken: loginResponse.refresh_token,
          })
        );
        toast.success("Login successful!");
        navigate("/dashboard");
      } else {
        throw new Error("Login failed");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Invalid OTP";

      // The authService now provides human-readable error messages
      // We can use them directly or enhance them for OTP-specific context
      let specificError = errorMessage;

      if (errorMessage.toLowerCase().includes("expired")) {
        specificError = "OTP has expired. Please request a new one.";
      } else if (
        errorMessage.toLowerCase().includes("invalid") ||
        errorMessage.toLowerCase().includes("wrong")
      ) {
        specificError =
          "Invalid OTP. Please check the 6-digit code and try again.";
      } else if (errorMessage.toLowerCase().includes("not found")) {
        specificError =
          "Account not found. Please check your email/phone number.";
      } else if (errorMessage.toLowerCase().includes("too many")) {
        specificError = "Too many failed attempts. Please try again later.";
      } else if (errorMessage.toLowerCase().includes("authentication")) {
        specificError = "OTP verification failed. Please try again.";
      }

      dispatch(loginFailure(specificError));
      toast.error(specificError);
      setErrors({ ...errors, otp: specificError });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToContact = () => {
    setStep("contact");
    setOtp("");
    setErrors({ contact: "", otp: "" });
  };

  return (
    <AuthLayout>
      <div className="max-w-md mx-auto w-full">
        {step === "contact" ? (
          <>
            <h1 className="text-3xl font-bold mb-2">Login with OTP</h1>
            <p className="text-gray-600 mb-8">
              Enter your email or mobile number to receive an OTP.
            </p>

            <form onSubmit={handleSendOTP} className="space-y-4">
              <div className="space-y-2">
                <AuthInput
                  type="text"
                  placeholder="Email or mobile number"
                  value={contact}
                  onChange={(e) => setContact(e.target.value)}
                  error={errors.contact}
                  disabled={isLoading}
                />
              </div>

              <AuthButton
                type="submit"
                isLoading={isLoading}
                className="w-full"
              >
                Send OTP
              </AuthButton>
            </form>

            <div className="text-center mt-6">
              <p className="text-gray-600">
                Remember your password?{" "}
                <Link to="/login" className="text-purple font-medium">
                  Login with Password
                </Link>
              </p>
            </div>
          </>
        ) : (
          <>
            <h1 className="text-3xl font-bold mb-2">Enter OTP</h1>
            <p className="text-gray-600 mb-8">
              We've sent a 6-digit code to {contact}
            </p>

            <div className="space-y-6">
              <div className="space-y-2">
                <OTPInput
                  length={6}
                  onComplete={setOtp}
                  className="justify-center"
                />
                {errors.otp && (
                  <p className="text-sm text-red-500 text-center">
                    {errors.otp}
                  </p>
                )}
              </div>

              <AuthButton
                onClick={handleVerifyOTP}
                isLoading={isLoading}
                className="w-full"
              >
                Verify OTP
              </AuthButton>

              <div className="text-center space-y-2">
                <p className="text-gray-600">
                  Didn't receive the code?{" "}
                  {canResend ? (
                    <button
                      type="button"
                      onClick={handleResendOTP}
                      className="text-purple hover:text-purple-dark font-medium"
                      disabled={isLoading}
                    >
                      Resend OTP
                    </button>
                  ) : (
                    <span className="text-gray-500">
                      Resend in {countdown}s
                    </span>
                  )}
                </p>

                <button
                  type="button"
                  onClick={handleBackToContact}
                  className="text-purple hover:text-purple-dark font-medium"
                  disabled={isLoading}
                >
                  Change contact details
                </button>
              </div>
            </div>
          </>
        )}

        <div className="text-center mt-6">
          <p className="text-gray-600">
            Don't have an account?{" "}
            <Link to="/register" className="text-purple font-medium">
              Register Now
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
};

export default LoginOTP;
