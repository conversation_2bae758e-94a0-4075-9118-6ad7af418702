import { useState, useEffect } from 'react';

export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        // Show reconnection message
        console.log('Connection restored');
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return { isOnline, wasOffline };
};

// Helper function to check if an error is network-related
export const isNetworkError = (error: Error): boolean => {
  const networkErrorMessages = [
    'network error',
    'fetch failed',
    'failed to fetch',
    'connection refused',
    'timeout',
    'network request failed',
  ];
  
  return networkErrorMessages.some(msg => 
    error.message.toLowerCase().includes(msg)
  );
};

// Helper function to get user-friendly error messages
export const getErrorMessage = (error: Error, isOnline: boolean): string => {
  if (!isOnline) {
    return 'No internet connection. Please check your network and try again.';
  }
  
  if (isNetworkError(error)) {
    return 'Network error. Please check your connection and try again.';
  }
  
  // Return the original error message for non-network errors
  return error.message;
};
