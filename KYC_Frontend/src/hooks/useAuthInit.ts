import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  loginSuccess,
  logout,
  selectIsAuthenticated,
} from "@/redux/slices/authSlice";
import { authService } from "@/services/authService";

export const useAuthInit = () => {
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if tokens are valid and not expired
        if (authService.areTokensValid()) {
          const { accessToken, refreshToken } = authService.getTokens();
          const storedUserData = authService.getStoredUserData();

          if (accessToken && refreshToken && storedUserData) {
            // Try to validate token with server
            try {
              const userData = await authService.getCurrentUser();

              // If successful, restore the auth state
              dispatch(
                loginSuccess({
                  user: userData,
                  accessToken,
                  refreshToken,
                })
              );
            } catch (error) {
              // Token is invalid on server, but try with stored data first
              console.warn(
                "Server token validation failed, using stored data:",
                error
              );

              // Use stored data if server is unreachable but tokens are not expired
              dispatch(
                loginSuccess({
                  user: storedUserData,
                  accessToken,
                  refreshToken,
                })
              );
            }
          } else {
            // Missing data, clear everything
            authService.clearTokens();
            dispatch(logout());
          }
        } else {
          // Tokens are expired or invalid
          authService.clearTokens();
          dispatch(logout());
        }
      } catch (error) {
        console.error("Auth initialization failed:", error);
        // Clear any corrupted data
        authService.clearTokens();
        dispatch(logout());
      } finally {
        setIsInitialized(true);
      }
    };

    if (!isInitialized && !isAuthenticated) {
      initializeAuth();
    } else if (!isInitialized) {
      setIsInitialized(true);
    }
  }, [dispatch, isAuthenticated, isInitialized]);

  return { isInitialized };
};
