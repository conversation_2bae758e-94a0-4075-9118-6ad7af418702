import { useState, useEffect, useCallback } from "react";
import { validateNoDuplicates, DuplicateValidationResult } from "@/utils/duplicateValidation";

/**
 * Hook for validating duplicate email and phone numbers in forms
 */
export const useDuplicateValidation = () => {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<DuplicateValidationResult | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  /**
   * Validate email and phone for duplicates
   * @param email - Email address to validate
   * @param phone_no - Phone number to validate
   * @param debounceMs - Debounce delay in milliseconds (default: 500)
   */
  const validateDuplicates = useCallback(
    async (email: string, phone_no: string, debounceMs: number = 500) => {
      // Clear previous results
      setValidationError(null);
      setValidationResult(null);

      // Skip validation if either field is empty
      if (!email.trim() || !phone_no.trim()) {
        return;
      }

      // Basic email validation
      const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
      if (!emailRegex.test(email)) {
        return; // Skip duplicate check for invalid email format
      }

      // Basic phone validation (10 digits)
      const phoneRegex = /^\d{10}$/;
      if (!phoneRegex.test(phone_no)) {
        return; // Skip duplicate check for invalid phone format
      }

      setIsValidating(true);

      // Debounce the validation
      const timeoutId = setTimeout(async () => {
        try {
          const result = await validateNoDuplicates(email, phone_no);
          setValidationResult(result);
          
          if (!result.isValid) {
            setValidationError(result.errors.join(". "));
          }
        } catch (error) {
          console.error("Duplicate validation error:", error);
          setValidationError("Unable to validate. Please try again.");
        } finally {
          setIsValidating(false);
        }
      }, debounceMs);

      // Cleanup function
      return () => {
        clearTimeout(timeoutId);
        setIsValidating(false);
      };
    },
    []
  );

  /**
   * Clear validation results
   */
  const clearValidation = useCallback(() => {
    setValidationResult(null);
    setValidationError(null);
    setIsValidating(false);
  }, []);

  /**
   * Check if current validation shows duplicates
   */
  const hasDuplicates = validationResult ? !validationResult.isValid : false;

  /**
   * Get specific duplicate status
   */
  const duplicateStatus = {
    emailExists: validationResult?.emailExists || false,
    phoneExists: validationResult?.phoneExists || false,
    hasAnyDuplicate: hasDuplicates,
  };

  return {
    // State
    isValidating,
    validationResult,
    validationError,
    hasDuplicates,
    duplicateStatus,

    // Actions
    validateDuplicates,
    clearValidation,
  };
};

/**
 * Hook for validating a single field (email or phone) for duplicates
 */
export const useSingleFieldDuplicateValidation = (type: "email" | "phone") => {
  const [isValidating, setIsValidating] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  /**
   * Validate a single field for duplicates
   * @param value - The value to validate
   * @param debounceMs - Debounce delay in milliseconds (default: 500)
   */
  const validateField = useCallback(
    async (value: string, debounceMs: number = 500) => {
      // Clear previous results
      setValidationError(null);
      setIsDuplicate(false);

      // Skip validation if field is empty
      if (!value.trim()) {
        return;
      }

      // Basic validation based on type
      if (type === "email") {
        const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
        if (!emailRegex.test(value)) {
          return; // Skip duplicate check for invalid email format
        }
      } else if (type === "phone") {
        const phoneRegex = /^\d{10}$/;
        if (!phoneRegex.test(value)) {
          return; // Skip duplicate check for invalid phone format
        }
      }

      setIsValidating(true);

      // Debounce the validation
      const timeoutId = setTimeout(async () => {
        try {
          // Use a dummy value for the other field to check only the target field
          const dummyEmail = type === "phone" ? "<EMAIL>" : value;
          const dummyPhone = type === "email" ? "0000000000" : value;
          
          const result = await validateNoDuplicates(dummyEmail, dummyPhone);
          
          const fieldExists = type === "email" ? result.emailExists : result.phoneExists;
          setIsDuplicate(fieldExists);
          
          if (fieldExists) {
            setValidationError(
              `This ${type} is already registered. Please use a different ${type}.`
            );
          }
        } catch (error) {
          console.error(`${type} duplicate validation error:`, error);
          setValidationError("Unable to validate. Please try again.");
        } finally {
          setIsValidating(false);
        }
      }, debounceMs);

      // Cleanup function
      return () => {
        clearTimeout(timeoutId);
        setIsValidating(false);
      };
    },
    [type]
  );

  /**
   * Clear validation results
   */
  const clearValidation = useCallback(() => {
    setIsDuplicate(false);
    setValidationError(null);
    setIsValidating(false);
  }, []);

  return {
    // State
    isValidating,
    isDuplicate,
    validationError,

    // Actions
    validateField,
    clearValidation,
  };
};
