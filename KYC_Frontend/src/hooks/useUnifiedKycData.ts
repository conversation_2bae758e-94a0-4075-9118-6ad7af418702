// Hook for managing unified KYC dashboard data with API integration

import { useMemo, useCallback } from "react";
import { useAppSelector } from "@/redux/hooks";
import {
  selectKYCApplications,
  selectApiLoading,
  selectApiErrors,
  selectApiData,
} from "@/redux/slices/kycSlice";
import { useKyc<PERSON><PERSON> } from "./useKycApi";
import {
  UnifiedKYCApplication,
  transformToUnifiedApplication,
} from "@/types/unifiedDashboard";

export interface UnifiedKycDataStats {
  totalApplications: number;
  completedApplications: number;
  inProgressApplications: number;
  averageCompletionRate: number;
  recentActivity: number; // Applications updated in last 24 hours
}

export interface UseUnifiedKycDataReturn {
  // Data
  applications: UnifiedKYCApplication[];
  stats: UnifiedKycDataStats;

  // API Status
  isLoading: boolean;
  hasErrors: boolean;
  apiErrors: Record<string, string | null>;
  lastSyncTimestamp: string | null;

  // Actions
  refreshData: () => void;
  getApplicationById: (id: string) => UnifiedKYCApplication | undefined;
  getApplicationsByStatus: (
    status: UnifiedKYCApplication["status"]
  ) => UnifiedKYCApplication[];

  // Filtering and Sorting
  filterApplications: (
    predicate: (app: UnifiedKYCApplication) => boolean
  ) => UnifiedKYCApplication[];
  sortApplications: (
    applications: UnifiedKYCApplication[],
    sortBy: keyof UnifiedKYCApplication,
    direction: "asc" | "desc"
  ) => UnifiedKYCApplication[];
}

export const useUnifiedKycData = (): UseUnifiedKycDataReturn => {
  // Redux selectors
  const rawApplications = useAppSelector(selectKYCApplications);
  const apiLoading = useAppSelector(selectApiLoading);
  const apiErrors = useAppSelector(selectApiErrors);
  const apiData = useAppSelector(selectApiData);

  // API integration hook
  const { refreshData, lastSyncTimestamp } = useKycApi();

  // Transform raw applications to unified format
  const applications = useMemo(() => {
    return rawApplications
      .filter(
        (app) =>
          // Only include applications with verified contact information
          app.verificationStatus?.email || app.verificationStatus?.mobile
      )
      .map((app) => {
        // Enhanced transformation with API data integration
        const unifiedApp = transformToUnifiedApplication(app, {
          personalInfo: apiData.personalInfoRecords,
          addressInfo: apiData.addressInfoRecords,
          documentInfo: apiData.documentInfoRecords,
        });

        // Enhance with API sync status
        const hasApiErrors = Object.values(apiErrors).some(
          (error) => error !== null
        );
        const isApiLoading = Object.values(apiLoading).some(
          (loading) => loading
        );

        return {
          ...unifiedApp,
          hasErrors: hasApiErrors,
          lastError: hasApiErrors
            ? "API synchronization issues detected"
            : undefined,
          lastErrorAt: hasApiErrors ? new Date().toISOString() : undefined,
          syncStatus: hasApiErrors
            ? ("failed" as const)
            : isApiLoading
            ? ("pending" as const)
            : ("synced" as const),
          lastSyncAt: lastSyncTimestamp || new Date().toISOString(),
        };
      });
  }, [rawApplications, apiData, apiErrors, apiLoading, lastSyncTimestamp]);

  // Calculate statistics
  const stats = useMemo((): UnifiedKycDataStats => {
    const total = applications.length;
    const completed = applications.filter((app) => app.isCompleted).length;
    const inProgress = applications.filter(
      (app) => !app.isCompleted && app.overallProgress > 0
    ).length;

    const totalProgress = applications.reduce(
      (sum, app) => sum + app.overallProgress,
      0
    );
    const averageCompletionRate =
      total > 0 ? Math.round(totalProgress / total) : 0;

    // Count applications updated in the last 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentActivity = applications.filter(
      (app) => new Date(app.updatedAt) > oneDayAgo
    ).length;

    return {
      totalApplications: total,
      completedApplications: completed,
      inProgressApplications: inProgress,
      averageCompletionRate,
      recentActivity,
    };
  }, [applications]);

  // Utility functions
  const getApplicationById = useCallback(
    (id: string): UnifiedKYCApplication | undefined => {
      return applications.find((app) => app.id === id);
    },
    [applications]
  );

  const getApplicationsByStatus = useCallback(
    (status: UnifiedKYCApplication["status"]): UnifiedKYCApplication[] => {
      return applications.filter((app) => app.status === status);
    },
    [applications]
  );

  const filterApplications = useCallback(
    (
      predicate: (app: UnifiedKYCApplication) => boolean
    ): UnifiedKYCApplication[] => {
      return applications.filter(predicate);
    },
    [applications]
  );

  const sortApplications = useCallback(
    (
      applicationsToSort: UnifiedKYCApplication[],
      sortBy: keyof UnifiedKYCApplication,
      direction: "asc" | "desc"
    ): UnifiedKYCApplication[] => {
      return [...applicationsToSort].sort((a, b) => {
        let aValue: string | number | boolean = a[sortBy] as
          | string
          | number
          | boolean;
        let bValue: string | number | boolean = b[sortBy] as
          | string
          | number
          | boolean;

        // Handle special cases
        if (sortBy === "updatedAt" || sortBy === "createdAt") {
          aValue = new Date(aValue as string).getTime();
          bValue = new Date(bValue as string).getTime();
        } else if (typeof aValue === "string") {
          aValue = aValue.toLowerCase();
          bValue = (bValue as string).toLowerCase();
        }

        if (direction === "asc") {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });
    },
    []
  );

  // Determine loading and error states
  const isLoading = Object.values(apiLoading).some((loading) => loading);
  const hasErrors = Object.values(apiErrors).some((error) => error !== null);

  return {
    // Data
    applications,
    stats,

    // API Status
    isLoading,
    hasErrors,
    apiErrors,
    lastSyncTimestamp,

    // Actions
    refreshData,
    getApplicationById,
    getApplicationsByStatus,

    // Filtering and Sorting
    filterApplications,
    sortApplications,
  };
};

// Additional utility hooks for specific use cases

export const useKycApplicationStats = () => {
  const { stats } = useUnifiedKycData();
  return stats;
};

export const useKycApplicationFilters = () => {
  const { applications, filterApplications } = useUnifiedKycData();

  const getCompletedApplications = useCallback(() => {
    return filterApplications((app) => app.isCompleted);
  }, [filterApplications]);

  const getInProgressApplications = useCallback(() => {
    return filterApplications(
      (app) => !app.isCompleted && app.overallProgress > 0
    );
  }, [filterApplications]);

  const getDraftApplications = useCallback(() => {
    return filterApplications((app) => app.status === "draft");
  }, [filterApplications]);

  const getApplicationsWithErrors = useCallback(() => {
    return filterApplications((app) => app.hasErrors);
  }, [filterApplications]);

  const getRecentApplications = useCallback(
    (days: number = 7) => {
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      return filterApplications((app) => new Date(app.updatedAt) > cutoffDate);
    },
    [filterApplications]
  );

  return {
    applications,
    getCompletedApplications,
    getInProgressApplications,
    getDraftApplications,
    getApplicationsWithErrors,
    getRecentApplications,
  };
};

// Hook for dashboard summary cards
export const useDashboardSummary = () => {
  const { stats } = useUnifiedKycData();

  const summaryCards = useMemo(
    () => [
      {
        title: "Total Applications",
        value: stats.totalApplications,
        change: `+${stats.recentActivity} in 24h`,
        trend: "up" as const,
        color: "blue",
      },
      {
        title: "Completed",
        value: stats.completedApplications,
        change: `${
          Math.round(
            (stats.completedApplications / stats.totalApplications) * 100
          ) || 0
        }% completion rate`,
        trend: "up" as const,
        color: "green",
      },
      {
        title: "In Progress",
        value: stats.inProgressApplications,
        change: `${stats.averageCompletionRate}% avg progress`,
        trend: "neutral" as const,
        color: "yellow",
      },
      {
        title: "Avg Completion",
        value: `${stats.averageCompletionRate}%`,
        change: "Across all applications",
        trend: "up" as const,
        color: "purple",
      },
    ],
    [stats]
  );

  return {
    summaryCards,
    stats,
    totalApplications: stats.totalApplications,
  };
};
