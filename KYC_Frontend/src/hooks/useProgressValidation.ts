import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  selectKYCApplications,
  selectCurrentApplicationId,
  validateAndFixProgress,
} from '@/redux/slices/kycSlice';
import {
  transformToUnifiedApplication,
  validateProgressConsistency,
} from '@/types/unifiedDashboard';

/**
 * Hook to automatically validate and fix progress inconsistencies
 * when the dashboard loads or when applications change
 */
export const useProgressValidation = () => {
  const dispatch = useAppDispatch();
  const applications = useAppSelector(selectKYCApplications);
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);

  useEffect(() => {
    if (!applications || applications.length === 0) return;

    let hasInconsistencies = false;
    const issues: string[] = [];

    // Validate all applications
    applications.forEach((application) => {
      const unifiedApp = transformToUnifiedApplication(application);
      const validation = validateProgressConsistency(unifiedApp);
      
      if (!validation.isConsistent) {
        hasInconsistencies = true;
        issues.push(`Application ${application.clientName || application.id}: ${validation.issues.join(', ')}`);
      }
    });

    // If inconsistencies found, fix them
    if (hasInconsistencies) {
      console.warn('Progress inconsistencies detected:', issues);
      dispatch(validateAndFixProgress());
    }
  }, [applications, currentApplicationId, dispatch]);

  // Return validation utilities for manual use
  const validateApplication = (applicationId: string) => {
    const application = applications?.find(app => app.id === applicationId);
    if (!application) return null;

    const unifiedApp = transformToUnifiedApplication(application);
    return validateProgressConsistency(unifiedApp);
  };

  const fixProgressInconsistencies = () => {
    dispatch(validateAndFixProgress());
  };

  return {
    validateApplication,
    fixProgressInconsistencies,
  };
};
