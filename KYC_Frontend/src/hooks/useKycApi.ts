import { useEffect, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  setApiLoading,
  setApiError,
  setPersonalInfoRecords,
  setAddressInfoRecords,
  setDocumentInfoRecords,
  selectApiLoading,
  selectApiErrors,
  selectApiData,
  selectLastSyncTimestamp,
} from "@/redux/slices/kycSlice";
import { kycService } from "@/services/kycService";
import { handleError } from "@/utils/errorHandling";

export const useKycApi = () => {
  const dispatch = useAppDispatch();
  const apiLoading = useAppSelector(selectApiLoading);
  const apiErrors = useAppSelector(selectApiErrors);
  const apiData = useAppSelector(selectApiData);
  const lastSyncTimestamp = useAppSelector(selectLastSyncTimestamp);

  // Fetch all personal information records
  const fetchPersonalInfoRecords = useCallback(async () => {
    try {
      dispatch(setApiLoading({ type: "personalInfo", loading: true }));
      const records = await kycService.getAllPersonalInformation();
      dispatch(setPersonalInfoRecords(records));
    } catch (error) {
      const appError = handleError(error, {
        operation: "fetchPersonalInfoRecords",
      });
      dispatch(
        setApiError({ type: "personalInfo", error: appError.userMessage })
      );
    }
  }, [dispatch]);

  // Fetch all address information records
  const fetchAddressInfoRecords = useCallback(async () => {
    try {
      dispatch(setApiLoading({ type: "addressInfo", loading: true }));
      const records = await kycService.getAllAddressInformation();
      dispatch(setAddressInfoRecords(records));
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch address information";
      dispatch(setApiError({ type: "addressInfo", error: errorMessage }));
    }
  }, [dispatch]);

  // Fetch all document information records
  const fetchDocumentInfoRecords = useCallback(async () => {
    try {
      dispatch(setApiLoading({ type: "documentInfo", loading: true }));
      const records = await kycService.getAllDocumentInformation();
      dispatch(setDocumentInfoRecords(records));
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch document information";
      dispatch(setApiError({ type: "documentInfo", error: errorMessage }));
    }
  }, [dispatch]);

  // Fetch all KYC applications
  const fetchKycApplications = useCallback(async () => {
    try {
      dispatch(setApiLoading({ type: "fetchingApplications", loading: true }));
      const applications = await kycService.getKYCApplications();
      // Note: This would need to be integrated with the applications state
      // For now, we'll just handle the loading state
      dispatch(setApiLoading({ type: "fetchingApplications", loading: false }));
      return applications;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch KYC applications";
      dispatch(
        setApiError({ type: "fetchingApplications", error: errorMessage })
      );
      return [];
    }
  }, [dispatch]);

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    await Promise.all([
      fetchPersonalInfoRecords(),
      fetchAddressInfoRecords(),
      fetchDocumentInfoRecords(),
      fetchKycApplications(),
    ]);
  }, [
    fetchPersonalInfoRecords,
    fetchAddressInfoRecords,
    fetchDocumentInfoRecords,
    fetchKycApplications,
  ]);

  // Auto-fetch data on mount and when needed
  useEffect(() => {
    // Only fetch if we don't have recent data (within last 5 minutes)
    const shouldFetch =
      !lastSyncTimestamp ||
      Date.now() - new Date(lastSyncTimestamp).getTime() > 5 * 60 * 1000;

    if (shouldFetch) {
      fetchAllData();
    }
  }, [fetchAllData, lastSyncTimestamp]);

  // Refresh data manually
  const refreshData = useCallback(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Check if any API call is loading
  const isLoading = Object.values(apiLoading).some((loading) => loading);

  // Check if there are any API errors
  const hasErrors = Object.values(apiErrors).some((error) => error !== null);

  return {
    // Data
    personalInfoRecords: apiData.personalInfoRecords,
    addressInfoRecords: apiData.addressInfoRecords,
    documentInfoRecords: apiData.documentInfoRecords,
    lastSyncTimestamp,

    // Loading states
    isLoading,
    apiLoading,

    // Error states
    hasErrors,
    apiErrors,

    // Actions
    fetchPersonalInfoRecords,
    fetchAddressInfoRecords,
    fetchDocumentInfoRecords,
    fetchKycApplications,
    fetchAllData,
    refreshData,
  };
};
